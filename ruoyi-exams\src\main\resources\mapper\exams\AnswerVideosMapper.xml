<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.AnswerVideosMapper">

    <resultMap type="AnswerVideos" id="AnswerVideosResult">
        <result property="id"    column="id"    />
        <result property="userExamId"    column="user_exam_id"    />
        <result property="userAnswerId"    column="user_answer_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="videoDuration"    column="video_duration"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAnswerVideosVo">
        select id, user_exam_id, user_answer_id, question_id, video_url, video_duration, start_time, end_time, create_by, create_time, update_by, update_time from answer_videos
    </sql>

    <select id="selectAnswerVideosList" parameterType="AnswerVideos" resultMap="AnswerVideosResult">
        <include refid="selectAnswerVideosVo"/>
        <where>
            <if test="userExamId != null "> and user_exam_id = #{userExamId}</if>
            <if test="userAnswerId != null "> and user_answer_id = #{userAnswerId}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="videoUrl != null  and videoUrl != ''"> and video_url = #{videoUrl}</if>
            <if test="videoDuration != null "> and video_duration = #{videoDuration}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>

    <select id="selectAnswerVideosById" parameterType="Long" resultMap="AnswerVideosResult">
        <include refid="selectAnswerVideosVo"/>
        where id = #{id}
    </select>

    <insert id="insertAnswerVideos" parameterType="AnswerVideos" useGeneratedKeys="true" keyProperty="id">
        insert into answer_videos
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userExamId != null">user_exam_id,</if>
            <if test="userAnswerId != null">user_answer_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="videoDuration != null">video_duration,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userExamId != null">#{userExamId},</if>
            <if test="userAnswerId != null">#{userAnswerId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="videoDuration != null">#{videoDuration},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAnswerVideos" parameterType="AnswerVideos">
        update answer_videos
        <trim prefix="SET" suffixOverrides=",">
            <if test="userExamId != null">user_exam_id = #{userExamId},</if>
            <if test="userAnswerId != null">user_answer_id = #{userAnswerId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="videoDuration != null">video_duration = #{videoDuration},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAnswerVideosById" parameterType="Long">
        delete from answer_videos where id = #{id}
    </delete>

    <delete id="deleteAnswerVideosByIds" parameterType="String">
        delete from answer_videos where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
