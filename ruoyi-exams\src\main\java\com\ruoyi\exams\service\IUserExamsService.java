package com.ruoyi.exams.service;

import com.ruoyi.exams.domain.UserExams;

import java.util.List;
import java.util.Map;

/**
 * 用户考试记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IUserExamsService {

    /**
     * 查询用户考试记录
     *
     * @param id 用户考试记录主键
     * @return 用户考试记录
     */
    public UserExams selectUserExamsById(Long id);

    /**
     * 查询用户考试记录列表
     *
     * @param userExams 用户考试记录
     * @return 用户考试记录集合
     */
    public List<UserExams> selectUserExamsList(UserExams userExams);

    /**
     * 新增用户考试记录
     *
     * @param userExams 用户考试记录
     * @return 结果
     */
    public int insertUserExams(UserExams userExams);

    /**
     * 修改用户考试记录
     *
     * @param userExams 用户考试记录
     * @return 结果
     */
    public int updateUserExams(UserExams userExams);

    /**
     * 批量删除用户考试记录
     *
     * @param ids 需要删除的用户考试记录主键集合
     * @return 结果
     */
    public int deleteUserExamsByIds(Long[] ids);

    /**
     * 删除用户考试记录信息
     *
     * @param id 用户考试记录主键
     * @return 结果
     */
    public int deleteUserExamsById(Long id);

    public Map queryEvaluationRecord(UserExams userExams);

    public List<UserExams> historyList(UserExams userExams);

    public Map<String, Object> percentageList(UserExams userExams);

}
