<template>
  <div class="screen-container backdrop-blur">
    <carousel-wrapper v-if="testQuestionList.length" v-model="currentIndex" :carousel-list="testQuestionList">
      <div v-for="(item, index) in testQuestionList" :key="index" class="carousel-item">
        <!-- 题目 -->
        <div class="topic flex">
          <p class="count shrink-0 m-0">{{ index + 1 }}</p>
          <p class="text m-0">{{ item[0].questionText }}</p>
        </div>
        <!-- 选项 -->
        <div class="options flex flex-wrap">
          <div
            class="screen-button flex justify-center items-center"
            :class="[option.isSelected ? 'bg-[#005eff] text-[#ffffff]' : 'bg-[#ffffff] text-[#005eff]']"
            v-for="option in item"
            :key="option.id"
            @click="handleSelect(item, option.optionId)"
          >
            <span class="option-count">{{ String.fromCharCode(64 + Number(option.sortOrder)) + '.' }}</span>
            <span class="option-text">{{ option.optionText }}</span>
          </div>
        </div>
      </div>
    </carousel-wrapper>

    <!-- 加载动画 -->
    <div v-if="showLoading" class="loading-overlay">
      <div class="loading-modal">
        <div class="loading-spinner"></div>
        <h2>{{ loadingText }}</h2>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onBeforeUnmount, getCurrentInstance, inject, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import CarouselWrapper from '@/views/screen/components/CarouselWrapper'
import { listTestQuestion } from '@/api/screen/test'
import { addExamsRecord } from '@/api/test/examsRecord'
import { addAnswers } from '@/api/test/answers'
import useUserStore from '@/store/modules/user'

const router = useRouter(),
  route = useRoute(),
  { proxy } = getCurrentInstance(),
  userStore = useUserStore(),
  testQuestionList = ref([]),
  examsRecordId = ref(''), // 考试记录id
  recorder = inject('recorder', null), // 注入视频录制组件方法
  showLoading = ref(false), // 是否显示加载动画
  loadingText = ref('请稍候...'), // 加载文本
  startTime = ref(''), // 考试开始时间
  pendingVideos = ref({}), // 待处理的视频 {topicId: {status, url}}
  allVideosUploaded = ref(false) // 是否所有视频都已上传
const currentIndex = ref(0) // 当前索引

const emit = defineEmits(['startRecording', 'stopRecording', 'handlePlay'])
onMounted(() => {
  // 记录开始时间，但不立即创建考试记录
  startTime.value = proxy.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')

  // 添加视频上传成功的事件监听
  window.addEventListener('video-upload-success', handleVideoUploadSuccess)

  // 开始录制视频
  emit('startRecording')
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('video-upload-success', handleVideoUploadSuccess)
})
// 监听currentIndex变化
watch(currentIndex, (newIndex, oldIndex) => {
  emit('handlePlay', `topic${testQuestionList.value[newIndex][0].questionId}`)
})

// 数据获取
const getList = () => {
  listTestQuestion({ id: route.query.id }).then(response => {
    testQuestionList.value = Object.values(response.data)
    // testQuestionList.value = Object.values(response.data).splice(0, 5)
    emit('handlePlay', `topic${testQuestionList.value[currentIndex.value][0].questionId}`)
  })
}
getList()

// 处理视频上传成功事件
const handleVideoUploadSuccess = event => {
  const { taskId, url, metadata, status } = event.detail
  console.log('视频上传事件:', taskId, url, status)

  // 更新对应题目的视频URL
  if (metadata && metadata.topicId) {
    const topicId = metadata.topicId
    const questionIndex = testQuestionList.value.findIndex(item => item[0].questionId === topicId)

    if (questionIndex !== -1) {
      // 更新真实URL
      testQuestionList.value[questionIndex].videoUrl = url
      console.log(`题目 ${topicId} 的视频URL已更新为: ${url}`)
    }

    // 更新待处理视频状态
    if (pendingVideos.value[topicId]) {
      // 如果是错误状态，也标记为完成，但记录错误信息
      if (status === 'error') {
        pendingVideos.value[topicId].status = 'error'
        pendingVideos.value[topicId].error = event.detail.response?.error || '处理失败'
        console.warn(`题目 ${topicId} 的视频处理失败，但不影响答题流程`)
      } else {
        pendingVideos.value[topicId].status = 'completed'
      }
      pendingVideos.value[topicId].url = url
    }

    // 检查是否所有视频都已上传
    checkAllVideosUploaded()
  }
}

// 检查是否所有视频都已上传
const checkAllVideosUploaded = () => {
  // 如果没有待处理的视频，直接返回true
  if (Object.keys(pendingVideos.value).length === 0) {
    allVideosUploaded.value = true
    return true
  }

  // 检查是否所有视频都已完成上传
  const allCompleted = Object.values(pendingVideos.value).every(video => video.status === 'completed' || video.status === 'error')

  allVideosUploaded.value = allCompleted
  return allCompleted
}

// 选项点击处理
const handleSelect = (item, optionId) => {
  // 选择选项
  item.forEach(option => (option.isSelected = option.optionId === optionId))

  // 获取当前题目ID
  const topicId = testQuestionList.value[currentIndex.value][0].questionId

  // 添加到待处理视频列表
  pendingVideos.value[topicId] = {
    status: 'pending',
    url: null
  }

  // 停止录制并创建上传任务
  emit('stopRecording', {
    id: 'temp', // 临时ID，因为考试记录还未创建
    topicId: topicId,
    callback: function (res) {
      // 保存临时URL，后续会被真实URL替换
      testQuestionList.value[currentIndex.value].videoUrl = res.url

      // 立即开始下一次录制，不等待上传完成
      setTimeout(() => {
        emit('startRecording') // 开始录制视频
      }, 300)

      // 处理下一步操作
      handleNextStep()
    }
  })
}

// 处理下一步操作（进入下一题或提交答案）
const handleNextStep = () => {
  // 找到未作答题目编号
  const qNum = testQuestionList.value.findIndex(item => !item.some(option => option.isSelected))

  if (qNum === -1) {
    // 所有题目已作答，准备完成测试
    handleTestCompletion()
  } else if (currentIndex.value < testQuestionList.value.length - 1) {
    // 进入下一题
    setTimeout(() => currentIndex.value++, 300)
  } else {
    // 跳转到未作答题目
    setTimeout(() => (currentIndex.value = qNum), 300)
  }
}

// 处理测试完成
const handleTestCompletion = () => {
  console.log('所有题目已完成，检查上传状态')

  // 显示加载动画
  showLoading.value = true
  loadingText.value = '正在处理视频，请稍候...'

  // 检查是否所有上传都已完成
  const checkAndCreateRecord = () => {
    try {
      // 检查是否所有视频都已上传
      if (checkAllVideosUploaded()) {
        console.log('所有视频已上传完成，创建考试记录')
        createExamRecord()
      } else {
        // 还有上传未完成，继续等待
        loadingText.value = '正在上传视频，请稍候...'
        setTimeout(checkAndCreateRecord, 500)
      }
    } catch (error) {
      console.error('检查上传完成状态出错:', error)
      // 出错时也创建考试记录，避免阻塞用户
      createExamRecord()
    }
  }

  // 开始检查
  checkAndCreateRecord()
}

// 创建考试记录
const createExamRecord = () => {
  loadingText.value = '正在创建考试记录...'

  // 计算得分
  const score = testQuestionList.value.reduce((acc, cur) => {
    return acc + cur.find(option => option.isSelected).score
  }, 0)

  // 获取当前时间作为结束时间
  const endTime = proxy.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')

  // 创建考试记录
  addExamsRecord({
    userId: userStore.id,
    examId: route.query.id,
    startTime: startTime.value,
    endTime: endTime,
    score: score
  })
    .then(res => {
      // 保存考试记录ID
      examsRecordId.value = res.data
      console.log('考试记录创建成功，ID:', examsRecordId.value)

      // 提交答案
      submitAnswers()
    })
    .catch(error => {
      console.error('创建考试记录失败:', error)
      showLoading.value = false
    })
}

// 提交答案并完成测试
const submitAnswers = () => {
  loadingText.value = '正在提交答案...'

  // 轮询调用答案新增接口
  const promises = testQuestionList.value.map(item => {
    // 获取真实视频URL
    const topicId = item[0].questionId
    let videoUrl = item.videoUrl

    // 如果有待处理视频中的URL，优先使用
    if (pendingVideos.value[topicId] && pendingVideos.value[topicId].url) {
      videoUrl = pendingVideos.value[topicId].url
    }

    // 提取文件名
    let urlName = ''
    if (videoUrl && videoUrl.includes('/')) {
      const parts = videoUrl.split('/')
      urlName = parts[parts.length - 1]
    }

    return addAnswers({
      userExamId: examsRecordId.value, // 用户考试记录表id
      questionId: item[0].questionId, // 题目表id
      optionId: item.find(option => option.isSelected)?.optionId || '', // 选项表id
      answerText: '', // 填空题答案
      videoUrl: videoUrl, // 视频存储地址
      urlName: urlName
    })
  })

  // 提交所有请求并跳转
  Promise.all(promises)
    .then(() => {
      // 关闭加载动画
      showLoading.value = false

      // 跳转到结果页
      router.push({ name: 'ScreenTestFinish', query: { id: examsRecordId.value } })
    })
    .catch(error => {
      console.error('提交答案失败:', error)
      showLoading.value = false
    })
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  .carousel-item {
    padding: 70px 140px;
    flex: 0 0 100%;
    min-width: 100%;
    box-sizing: border-box;
    backface-visibility: hidden;
  }
  /* 每项题目的样式 */
  .topic {
    column-gap: 41px;
    .count {
      width: 96px;
      height: 120px;
      background-image: url(@/assets/images//screen/bg-count.png);
      background-size: 100% 100%;
      padding-top: 36px;
      text-align: center;
      font-family: Archivo Black;
      font-size: 56px;
      font-weight: normal;
      line-height: 56px;
      color: #0d478e;
    }
    .text {
      margin-top: 14px;
      font-family: AlibabaPuHuiTiBold;
      font-size: 72px;
      line-height: 106px;
    }
  }

  .options {
    margin-top: 172px;
    gap: 40px;
    .screen-button {
      column-gap: 16px;
      width: calc(50% - 20px);
      // width: 640px;
      height: 160px;
      border-radius: 32px;
      font-family: AlibabaPuHuiTiBold;
      font-size: 60px;
      line-height: 60px;
      transition: all 0.2s;

      &:active {
        transform: scale(0.96);
        opacity: 0.9;
      }
    }
  }
}

/* 加载动画样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-modal {
  background-color: white;
  border-radius: 32px;
  padding: 60px;
  width: 80%;
  max-width: 1200px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

  h2 {
    font-family: AlibabaPuHuiTiBold;
    font-size: 72px;
    color: #005eff;
    margin-top: 40px;
  }

  .loading-spinner {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border: 16px solid #f3f3f3;
    border-top: 16px solid #005eff;
    border-radius: 50%;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
