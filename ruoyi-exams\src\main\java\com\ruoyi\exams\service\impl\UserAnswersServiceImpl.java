package com.ruoyi.exams.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.UserAnswers;
import com.ruoyi.exams.mapper.UserAnswersMapper;
import com.ruoyi.exams.service.IUserAnswersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户答案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class UserAnswersServiceImpl implements IUserAnswersService {

    @Autowired
    private UserAnswersMapper userAnswersMapper;

    /**
     * 查询用户答案
     *
     * @param id 用户答案主键
     * @return 用户答案
     */
    @Override
    public UserAnswers selectUserAnswersById(Long id)
    {
        return userAnswersMapper.selectUserAnswersById(id);
    }

    /**
     * 查询用户答案列表
     *
     * @param userAnswers 用户答案
     * @return 用户答案
     */
    @Override
    public List<UserAnswers> selectUserAnswersList(UserAnswers userAnswers)
    {
        return userAnswersMapper.selectUserAnswersList(userAnswers);
    }

    /**
     * 新增用户答案
     *
     * @param userAnswers 用户答案
     * @return 结果
     */
    @Override
    public int insertUserAnswers(UserAnswers userAnswers)
    {
        userAnswers.setCreateBy(SecurityUtils.getUserId().toString());
        userAnswers.setCreateTime(DateUtils.getNowDate());
        return userAnswersMapper.insertUserAnswers(userAnswers);
    }

    /**
     * 修改用户答案
     *
     * @param userAnswers 用户答案
     * @return 结果
     */
    @Override
    public int updateUserAnswers(UserAnswers userAnswers)
    {
        userAnswers.setUpdateBy(SecurityUtils.getUserId().toString());
        userAnswers.setUpdateTime(DateUtils.getNowDate());
        return userAnswersMapper.updateUserAnswers(userAnswers);
    }

    /**
     * 批量删除用户答案
     *
     * @param ids 需要删除的用户答案主键
     * @return 结果
     */
    @Override
    public int deleteUserAnswersByIds(Long[] ids)
    {
        return userAnswersMapper.deleteUserAnswersByIds(ids);
    }

    /**
     * 删除用户答案信息
     *
     * @param id 用户答案主键
     * @return 结果
     */
    @Override
    public int deleteUserAnswersById(Long id)
    {
        return userAnswersMapper.deleteUserAnswersById(id);
    }

}
