<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.OptionsMapper">

    <resultMap type="Options" id="OptionsResult">
        <result property="id"    column="id"    />
        <result property="questionId"    column="question_id"    />
        <result property="optionText"    column="option_text"    />
        <result property="isCorrect"    column="is_correct"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOptionsVo">
        select id, question_id, option_text, is_correct, sort_order, create_by, create_time, update_by, update_time from options
    </sql>

    <select id="selectOptionsList" parameterType="Options" resultMap="OptionsResult">
        <include refid="selectOptionsVo"/>
        <where>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="optionText != null  and optionText != ''"> and option_text = #{optionText}</if>
            <if test="isCorrect != null "> and is_correct = #{isCorrect}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
        </where>
    </select>

    <select id="selectOptionsById" parameterType="Long" resultMap="OptionsResult">
        <include refid="selectOptionsVo"/>
        where id = #{id}
    </select>

    <insert id="insertOptions" parameterType="Options" useGeneratedKeys="true" keyProperty="id">
        insert into options
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionId != null">question_id,</if>
            <if test="optionText != null">option_text,</if>
            <if test="isCorrect != null">is_correct,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionId != null">#{questionId},</if>
            <if test="optionText != null">#{optionText},</if>
            <if test="isCorrect != null">#{isCorrect},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOptions" parameterType="Options">
        update options
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionText != null">option_text = #{optionText},</if>
            <if test="isCorrect != null">is_correct = #{isCorrect},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOptionsById" parameterType="Long">
        delete from options where id = #{id}
    </delete>

    <delete id="deleteOptionsByIds" parameterType="String">
        delete from options where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryTestQuestion" resultType="map">
        SELECT
            b.id questionId,
            b.question_text questionText,
            b.question_type questionType,
            b.score_order scoreOrder,
            b.reverse_score reverseScore,
            c.id optionId,
            c.option_text optionText,
            c.score,
            c.sort_order sortOrder
        from exams a left join questions b on a.id = b.exam_id
        left join options c on b.id = c.question_id
        <where>
            a.id = #{id}
        </where>
        order by b.score_order, c.sort_order
    </select>

</mapper>
