/**
 * 视频录制模块
 * 提供视频录制和媒体流管理功能
 */
import { ref, onBeforeUnmount } from 'vue'

export function useVideoRecording(options = {}) {
  // 视频录制相关状态
  const mediaStream = ref(null)
  const mediaRecorder = ref(null)
  const recordedChunks = ref([])
  const originalBlob = ref(null)
  const status = ref('idle') // idle | recording | converting | uploading | downloading
  const allowsRecord = ref(true) // 是否允许录制

  // 答题时间控制
  const minAnswerSeconds = options.minAnswerSeconds || 4 // 最小答题停留秒数
  const showTooFastTip = ref(false)
  let answerStartTime = 0 // 答题开始时间戳

  /**
   * 初始化媒体流，兼容无麦克风或无权限情况
   * @param {Object} videoConfig - 视频配置
   * @param {boolean|Object} audioConfig - 音频配置
   * @param {HTMLVideoElement} previewElement - 预览元素
   * @param {Function} onVideoReady - 视频就绪回调
   * @returns {Promise<boolean>} 是否成功初始化
   */
  const initStream = async (videoConfig, audioConfig, previewElement, onVideoReady) => {
    try {
      // 优先尝试音视频都开
      mediaStream.value = await navigator.mediaDevices.getUserMedia({
        video: videoConfig,
        audio: audioConfig
      })

      if (previewElement) {
        previewElement.srcObject = mediaStream.value
        previewElement.play?.()
        // 监听video的canplay事件，等画面ready后再初始化detector和检测循环
        if (onVideoReady) {
          previewElement.oncanplay = onVideoReady
        }
      }

      return true
    } catch (error) {
      // 如果是音频设备问题，再降级为只开视频
      console.error('初始化媒体流失败:', error)

      try {
        mediaStream.value = await navigator.mediaDevices.getUserMedia({
          video: videoConfig,
          audio: false
        })

        if (previewElement) {
          previewElement.srcObject = mediaStream.value
          previewElement.play?.()
          if (onVideoReady) {
            previewElement.oncanplay = onVideoReady
          }
        }

        return true
      } catch (err2) {
        allowsRecord.value = false
        console.error('摄像头访问失败:', err2)
        return false
      }
    }
  }

  /**
   * 获取最佳媒体格式
   * @returns {string} 媒体格式
   */
  const getBestMimeType = () => {
    const preferences = [
      'video/mp4; codecs="avc1.640028"', // H.264 High Profile
      'video/mp4; codecs="avc1.42E01E"', // H.264 Baseline
      'video/webm; codecs=h264', // WebM with H.264
      'video/webm; codecs=vp9', // WebM VP9
      'video/webm' // 通用WebM
    ]
    return preferences.find(MediaRecorder.isTypeSupported) || ''
  }

  /**
   * 开始录制
   * @returns {Promise<boolean>} 是否成功开始录制
   */
  const start = async () => {
    if (status.value !== 'idle' || !mediaStream.value) return false

    // 答题开始时间
    answerStartTime = Date.now()
    showTooFastTip.value = false

    try {
      recordedChunks.value = []
      mediaRecorder.value = new MediaRecorder(mediaStream.value, {
        mimeType: getBestMimeType(),
        videoBitsPerSecond: 2500000
      })

      // 添加录制开始事件处理
      mediaRecorder.value.onstart = () => {
        console.log('MediaRecorder 开始录制')
      }

      // 添加录制停止事件处理
      mediaRecorder.value.onstop = () => {
        console.log('MediaRecorder 停止录制，数据块数量:', recordedChunks.value.length)
      }

      // 添加错误处理
      mediaRecorder.value.onerror = (event) => {
        console.error('MediaRecorder 错误:', event.error)
      }

      // 数据可用事件处理
      mediaRecorder.value.ondataavailable = e => {
        console.log('数据块大小:', e.data.size)
        if (e.data.size > 0) recordedChunks.value.push(e.data)
      }

      // 使用更小的时间片段，确保短时间录制也能获取数据
      mediaRecorder.value.start(500) // 500毫秒数据切片，提高短时间录制的数据采集频率
      status.value = 'recording'
      return true
    } catch (error) {
      console.error('开始录制失败:', error)
      return false
    }
  }

  /**
   * 停止录制
   * @returns {Promise<Blob>} 录制的视频Blob
   */
  const stop = async () => {
    if (!allowsRecord.value || status.value !== 'recording') return null

    // 判断答题时长是否满足最小要求
    const answerDuration = (Date.now() - answerStartTime) / 1000
    console.log('录制时长:', answerDuration, '秒')

    if (answerDuration < minAnswerSeconds) {
      showTooFastTip.value = true
      setTimeout(() => {
        showTooFastTip.value = false
      }, 2000)
      return null // 不执行停止录制
    }

    try {
      // 确保在停止录制前先手动请求一个数据块
      // 这对于短时间录制特别重要，确保至少有一个数据块
      if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
        mediaRecorder.value.requestData();

        // 添加短暂延迟确保数据被收集
        await new Promise(resolve => setTimeout(resolve, 100));

        // 停止录制
        mediaRecorder.value.stop();
      }
    } catch (error) {
      console.error('停止录制时出错:', error);
    }

    // 检查是否有录制数据
    if (recordedChunks.value.length === 0) {
      console.error('没有录制到任何数据块');
      status.value = 'idle';
      mediaRecorder.value = null;
      return null;
    }

    // 记录数据块信息
    console.log(`录制完成: ${recordedChunks.value.length} 个数据块`);
    recordedChunks.value.forEach((chunk, index) => {
      console.log(`数据块 ${index + 1} 大小: ${chunk.size} 字节`);
    });

    status.value = 'idle';

    // 保存录制器使用的MIME类型
    const mimeType = mediaRecorder.value?.mimeType || 'video/webm';
    mediaRecorder.value = null;

    // 创建原始视频数据
    const rawBlob = new Blob(recordedChunks.value, { type: mimeType });
    console.log(`生成的视频Blob大小: ${rawBlob.size} 字节, 类型: ${rawBlob.type}`);

    // 检查Blob是否有效
    if (rawBlob.size === 0) {
      console.error('生成的视频Blob大小为0，录制失败');
      return null;
    }

    // 保存原始数据
    originalBlob.value = rawBlob;
    return rawBlob;
  }

  /**
   * 清理资源
   */
  const cleanup = () => {
    mediaStream.value?.getTracks().forEach(track => track.stop())
    mediaStream.value = null
    mediaRecorder.value = null
    recordedChunks.value = []
    originalBlob.value = null
  }

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    mediaStream,
    status,
    allowsRecord,
    showTooFastTip,
    initStream,
    start,
    stop,
    cleanup
  }
}
