package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.Exams;
import com.ruoyi.exams.service.IExamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 试卷Controller
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@RestController
@RequestMapping("/exams")
public class ExamsController extends BaseController {

    @Autowired
    private IExamsService examsService;

    /**
     * 查询试卷列表
     */
    @PreAuthorize("@ss.hasPermi('system:exams:list')")
    @GetMapping("/list")
    public TableDataInfo list(Exams exams)
    {
        startPage();
        List<Exams> list = examsService.selectExamsList(exams);
        return getDataTable(list);
    }

    /**
     * 导出试卷列表
     */
    @PreAuthorize("@ss.hasPermi('system:exams:export')")
    @Log(title = "试卷", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Exams exams)
    {
        List<Exams> list = examsService.selectExamsList(exams);
        ExcelUtil<Exams> util = new ExcelUtil<Exams>(Exams.class);
        util.exportExcel(response, list, "试卷数据");
    }

    /**
     * 获取试卷详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:exams:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examsService.selectExamsById(id));
    }

    /**
     * 新增试卷
     */
    @PreAuthorize("@ss.hasPermi('system:exams:add')")
    @Log(title = "试卷", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Exams exams)
    {
        return toAjax(examsService.insertExams(exams));
    }

    /**
     * 修改试卷
     */
    @PreAuthorize("@ss.hasPermi('system:exams:edit')")
    @Log(title = "试卷", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Exams exams)
    {
        return toAjax(examsService.updateExams(exams));
    }

    /**
     * 删除试卷
     */
    @PreAuthorize("@ss.hasPermi('system:exams:remove')")
    @Log(title = "试卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examsService.deleteExamsByIds(ids));
    }

}
