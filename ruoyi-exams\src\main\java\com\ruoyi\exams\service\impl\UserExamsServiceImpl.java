package com.ruoyi.exams.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.UserExams;
import com.ruoyi.exams.mapper.UserExamsMapper;
import com.ruoyi.exams.service.IUserExamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户考试记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class UserExamsServiceImpl implements IUserExamsService {

    @Autowired
    private UserExamsMapper userExamsMapper;

    /**
     * 查询用户考试记录
     *
     * @param id 用户考试记录主键
     * @return 用户考试记录
     */
    @Override
    public UserExams selectUserExamsById(Long id)
    {
        return userExamsMapper.selectUserExamsById(id);
    }

    /**
     * 查询用户考试记录列表
     *
     * @param userExams 用户考试记录
     * @return 用户考试记录
     */
    @Override
    public List<UserExams> selectUserExamsList(UserExams userExams)
    {
        return userExamsMapper.selectUserExamsList(userExams);
    }

    /**
     * 新增用户考试记录
     *
     * @param userExams 用户考试记录
     * @return 结果
     */
    @Override
    public int insertUserExams(UserExams userExams)
    {
        int result = 0;
        userExams.setCreateBy(SecurityUtils.getUserId().toString());
        userExams.setCreateTime(DateUtils.getNowDate());
        int i = userExamsMapper.insertUserExams(userExams);
        if (i > 0) {
            result = Integer.parseInt(userExams.getId().toString());
        }
        return result;
    }

    /**
     * 修改用户考试记录
     *
     * @param userExams 用户考试记录
     * @return 结果
     */
    @Override
    public int updateUserExams(UserExams userExams)
    {
        userExams.setUpdateBy(SecurityUtils.getUserId().toString());
        userExams.setUpdateTime(DateUtils.getNowDate());
        return userExamsMapper.updateUserExams(userExams);
    }

    /**
     * 批量删除用户考试记录
     *
     * @param ids 需要删除的用户考试记录主键
     * @return 结果
     */
    @Override
    public int deleteUserExamsByIds(Long[] ids)
    {
        return userExamsMapper.deleteUserExamsByIds(ids);
    }

    /**
     * 删除用户考试记录信息
     *
     * @param id 用户考试记录主键
     * @return 结果
     */
    @Override
    public int deleteUserExamsById(Long id)
    {
        return userExamsMapper.deleteUserExamsById(id);
    }

    @Override
    public Map queryEvaluationRecord(UserExams userExams) {
        List<Map<String, Object>> list = userExamsMapper.queryEvaluationRecord(userExams);
        Map<Object, List<Map<String, Object>>> gather = list.stream().collect(Collectors.groupingBy(
                map -> map.get("questionId"),
                LinkedHashMap::new,
                Collectors.toList()
        ));
        return gather;
    }

    @Override
    public List<UserExams> historyList(UserExams userExams) {
        List<UserExams> list = userExamsMapper.historyList(userExams);
        return list;
    }

    @Override
    public Map<String, Object> percentageList(UserExams userExams) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> pie = new HashMap<>();
        int total = 0;
        int positiveCount = 0;
        int negativeCount = 0;
        int othersCount = 0;
        List<Map<String, Object>> list = userExamsMapper.percentageList(userExams);
        for (Map<String, Object> m : list) {
            String emotion = m.get("emotion").toString();
            if (emotion.equals("开心") || emotion.equals("惊讶")) {
                positiveCount++;
            } else if (emotion.equals("压抑") || emotion.equals("悲伤") || emotion.equals("厌恶") || emotion.equals("恐惧")) {
                negativeCount++;
            } else {
                othersCount++;
            }
        }
        total = positiveCount + negativeCount + othersCount;
        double positivePercent = 0.0;
        double negativePercent = 0.0;
        double othersPercent = 0.0;
        if (total > 0) {
            positivePercent = (positiveCount * 100.0) / total;
            negativePercent = (negativeCount * 100.0) / total;
            othersPercent = (othersCount * 100.0) / total;
        }
        DecimalFormat df = new DecimalFormat("#.##");
        pie.put("positive", df.format(positivePercent));
        pie.put("negative", df.format(negativePercent));
        pie.put("others", df.format(othersPercent));
        map.put("list", list);
        map.put("pie", pie);
        return map;
    }

}
