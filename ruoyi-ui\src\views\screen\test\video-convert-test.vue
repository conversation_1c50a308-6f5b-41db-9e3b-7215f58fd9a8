<template>
  <div class="video-convert-container">
    <h1 class="test-title">视频编码格式检测与转换工具</h1>
    
    <!-- 浏览器兼容性警告 -->
    <div v-if="!isSharedArrayBufferSupported" class="compatibility-warning">
      <p>⚠️ 您的浏览器不支持 SharedArrayBuffer，这可能导致检测失败。</p>
      <p>请尝试以下解决方案：</p>
      <ul>
        <li>使用最新版 Chrome 或 Edge 浏览器</li>
        <li>确保网站使用 HTTPS 并配置了正确的 COOP/COEP 头</li>
      </ul>
    </div>
    
    <!-- 文件上传区域 -->
    <div class="upload-area">
      <input type="file" accept="video/mp4" @change="handleFileSelect" ref="fileInput" class="file-input" />
      <button class="select-btn" @click="$refs.fileInput.click()">选择MP4视频文件</button>
      <div v-if="selectedFile" class="file-info">
        当前文件: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
      </div>
    </div>
    
    <!-- 分析按钮 -->
    <div class="analyze-section">
      <button class="analyze-btn" @click="analyzeVideo" :disabled="!selectedFile || isAnalyzing">
        {{ isAnalyzing ? '分析中...' : '分析视频编码' }}
      </button>
    </div>
    
    <!-- 转换按钮 -->
    <div v-if="videoInfo" class="convert-section">
      <button class="convert-btn" @click="convertVideo" :disabled="isConverting">
        {{ isConverting ? '转换中...' : '转换为移动端兼容格式' }}
      </button>
    </div>
    
    <!-- 进度条 -->
    <div v-if="isAnalyzing || isConverting" class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: `${analyzeProgress}%` }"></div>
      </div>
      <div class="progress-text">{{ analyzeProgress.toFixed(1) }}%</div>
      <div class="status-text">{{ statusMessage }}</div>
    </div>
    
    <!-- 视频预览 -->
    <div v-if="selectedFile" class="video-preview-container">
      <h2>原始视频预览</h2>
      <video controls width="400" ref="videoPreview">
        <source :src="videoUrl" type="video/mp4">
        您的浏览器不支持视频播放
      </video>
    </div>
    
    <!-- 转换后视频预览 -->
    <div v-if="convertedVideoUrl" class="video-preview-container">
      <h2>转换后视频预览</h2>
      <video controls width="400">
        <source :src="convertedVideoUrl" type="video/mp4">
        您的浏览器不支持视频播放
      </video>
      <div class="download-section">
        <a :href="convertedVideoUrl" download="converted.mp4" class="download-btn">下载转换后的视频</a>
      </div>
    </div>
    
    <!-- 分析结果 -->
    <div v-if="videoInfo" class="result-container">
      <h2>视频编码信息</h2>
      <div class="info-card">
        <div class="info-item" v-if="videoInfo.format">
          <div class="info-label">容器格式:</div>
          <div class="info-value">{{ videoInfo.format }}</div>
        </div>
        <div class="info-item" v-if="videoInfo.videoCodec">
          <div class="info-label">视频编码:</div>
          <div class="info-value">{{ videoInfo.videoCodec }}</div>
        </div>
        <div class="info-item" v-if="videoInfo.audioCodec">
          <div class="info-label">音频编码:</div>
          <div class="info-value">{{ videoInfo.audioCodec }}</div>
        </div>
        <div class="info-item" v-if="videoInfo.resolution">
          <div class="info-label">分辨率:</div>
          <div class="info-value">{{ videoInfo.resolution }}</div>
        </div>
        <div class="info-item" v-if="videoInfo.duration">
          <div class="info-label">时长:</div>
          <div class="info-value">{{ videoInfo.duration }}</div>
        </div>
        <div class="info-item" v-if="videoInfo.bitrate">
          <div class="info-label">比特率:</div>
          <div class="info-value">{{ videoInfo.bitrate }}</div>
        </div>
        <div class="info-item" v-if="videoInfo.compatibility">
          <div class="info-label">移动端兼容性:</div>
          <div class="info-value" :class="videoInfo.compatibility === '良好' ? 'good-compatibility' : 'poor-compatibility'">
            {{ videoInfo.compatibility }}
          </div>
        </div>
      </div>
      
      <div class="raw-info">
        <h3>详细信息</h3>
        <pre>{{ videoInfo.rawInfo }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ffmpeg, fetchFile } from '@/utils/ffmpeg'

// 状态变量
const fileInput = ref(null)
const selectedFile = ref(null)
const videoPreview = ref(null)
const videoUrl = ref('')
const convertedVideoUrl = ref('')
const isAnalyzing = ref(false)
const isConverting = ref(false)
const analyzeProgress = ref(0)
const statusMessage = ref('')
const videoInfo = ref(null)
const ffmpegOutput = ref([]) // 存储 FFmpeg 输出信息

// 浏览器兼容性检测
const isSharedArrayBufferSupported = ref(typeof SharedArrayBuffer !== 'undefined')

// 文件选择处理
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file && file.type.includes('video/mp4')) {
    selectedFile.value = file
    
    // 释放之前的URL
    if (videoUrl.value) {
      URL.revokeObjectURL(videoUrl.value)
    }
    if (convertedVideoUrl.value) {
      URL.revokeObjectURL(convertedVideoUrl.value)
      convertedVideoUrl.value = ''
    }
    
    // 创建新的URL用于预览
    videoUrl.value = URL.createObjectURL(file)
    
    // 重置之前的分析结果
    videoInfo.value = null
    ffmpegOutput.value = []
  } else {
    alert('请选择MP4格式的视频文件')
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 分析视频编码
const analyzeVideo = async () => {
  if (!selectedFile.value || isAnalyzing.value) return
  
  isAnalyzing.value = true
  analyzeProgress.value = 0
  statusMessage.value = '准备分析...'
  ffmpegOutput.value = [] // 清空之前的输出
  
  try {
    // 加载 FFmpeg
    if (!ffmpeg.isLoaded()) {
      statusMessage.value = '加载分析工具...'
      await ffmpeg.load()
    }
    
    // 设置进度回调
    ffmpeg.setProgress(({ ratio }) => {
      if (ratio && !isNaN(ratio)) {
        analyzeProgress.value = ratio * 100
      }
    })
    
    // 设置日志回调以捕获输出
    ffmpeg.setLogger(({ type, message }) => {
      // 只收集错误输出，因为 FFmpeg 的分析信息通常输出到 stderr
      if (type === 'fferr') {
        ffmpegOutput.value.push(message)
      }
    })
    
    // 写入输入文件
    statusMessage.value = '处理视频文件...'
    ffmpeg.FS('writeFile', 'input.mp4', await fetchFile(selectedFile.value))
    
    // 执行分析命令
    statusMessage.value = '分析中...'
    await ffmpeg.run('-i', 'input.mp4')
    
  } catch (error) {
    // FFmpeg 在分析文件时会抛出错误，但这是正常的
    // 因为 ffmpeg -i 命令在没有输出文件时会返回错误码
    console.log('FFmpeg 分析完成')
  } finally {
    // 无论是否有错误，都处理收集到的输出
    const rawInfo = ffmpegOutput.value.join('\n')
    
    // 解析视频信息
    videoInfo.value = parseVideoInfo(rawInfo)
    videoInfo.value.rawInfo = rawInfo
    
    // 评估移动端兼容性
    evaluateCompatibility(videoInfo.value)
    
    statusMessage.value = '分析完成!'
    isAnalyzing.value = false
  }
}

// 解析视频信息
const parseVideoInfo = (rawInfo) => {
  const info = {}
  
  // 提取容器格式
  const formatMatch = rawInfo.match(/Input #0, ([^,]+),/)
  if (formatMatch) {
    info.format = formatMatch[1]
  }
  
  // 提取视频编码
  const videoCodecMatch = rawInfo.match(/Stream #.*?Video: ([^,]+)/)
  if (videoCodecMatch) {
    info.videoCodec = videoCodecMatch[1].trim()
  }
  
  // 提取音频编码
  const audioCodecMatch = rawInfo.match(/Stream #.*?Audio: ([^,]+)/)
  if (audioCodecMatch) {
    info.audioCodec = audioCodecMatch[1].trim()
  }
  
  // 提取分辨率
  const resolutionMatch = rawInfo.match(/(\d+x\d+)/)
  if (resolutionMatch) {
    info.resolution = resolutionMatch[1]
  }
  
  // 提取时长
  const durationMatch = rawInfo.match(/Duration: ([^,]+)/)
  if (durationMatch) {
    info.duration = durationMatch[1].trim()
  }
  
  // 提取比特率
  const bitrateMatch = rawInfo.match(/bitrate: ([^,]+)/)
  if (bitrateMatch) {
    info.bitrate = bitrateMatch[1].trim()
  }
  
  return info
}

// 评估移动端兼容性
const evaluateCompatibility = (info) => {
  let isCompatible = true
  const issues = []
  
  // 检查视频编码
  if (info.videoCodec && info.videoCodec.includes('High')) {
    isCompatible = false
    issues.push('使用了H.264 High Profile，部分移动设备可能不支持')
  }
  
  // 检查分辨率
  if (info.resolution) {
    const [width, height] = info.resolution.split('x').map(Number)
    if (width > 1280 || height > 1920) {
      isCompatible = false
      issues.push('分辨率过高，部分移动设备可能无法流畅播放')
    }
  }
  
  // 检查流顺序
  const videoStreamFirst = /Stream #0:0.*?Video:/.test(info.rawInfo)
  if (!videoStreamFirst) {
    isCompatible = false
    issues.push('视频流不在第一位，可能导致某些播放器无法识别')
  }
  
  // 检查色彩空间
  if (info.rawInfo.includes('smpte170m')) {
    isCompatible = false
    issues.push('使用了非标准色彩空间，可能影响兼容性')
  }
  
  // 检查时基和帧率
  const tbnMatch = info.rawInfo.match(/(\d+) tbn/)
  if (tbnMatch && parseInt(tbnMatch[1]) > 10000) {
    isCompatible = false
    issues.push('时基(tbn)值过高，可能导致某些播放器解析错误')
  }
  
  // 检查帧率
  const fpsMatch = info.rawInfo.match(/(\d+(?:\.\d+)?) fps/)
  if (fpsMatch && parseFloat(fpsMatch[1]) > 30) {
    isCompatible = false
    issues.push('帧率过高，部分移动设备可能无法流畅播放')
  }
  
  // 设置兼容性评估结果
  info.compatibility = isCompatible ? '良好' : '可能存在问题'
  info.compatibilityIssues = issues
}

// 转换视频为移动端兼容格式
const convertVideo = async () => {
  if (!selectedFile.value || isConverting.value) return
  
  isConverting.value = true
  analyzeProgress.value = 0
  statusMessage.value = '准备转换...'
  
  try {
    // 加载 FFmpeg
    if (!ffmpeg.isLoaded()) {
      statusMessage.value = '加载转换工具...'
      await ffmpeg.load()
    }
    
    // 设置进度回调
    ffmpeg.setProgress(({ ratio }) => {
      if (ratio && !isNaN(ratio)) {
        analyzeProgress.value = ratio * 100
      }
    })
    
    // 写入输入文件
    statusMessage.value = '处理视频文件...'
    ffmpeg.FS('writeFile', 'input.mp4', await fetchFile(selectedFile.value))
    
    // 执行转换命令 - 增强版本
    statusMessage.value = '转换中...'
    await ffmpeg.run(
      '-i', 'input.mp4',
      '-vf', 'scale=1280:-2', // 降低分辨率到1280宽度，保持宽高比
      '-r', '30', // 固定帧率为30fps
      '-c:v', 'libx264', // 使用H.264编码
      '-profile:v', 'baseline', // 使用Baseline Profile（最大兼容性）
      '-level', '3.0', // 设置兼容级别
      '-preset', 'medium', // 编码速度和质量的平衡
      '-tune', 'fastdecode', // 优化解码速度
      '-pix_fmt', 'yuv420p', // 确保像素格式兼容
      '-c:a', 'aac', // 音频使用AAC编码
      '-b:a', '128k', // 音频比特率
      '-ar', '44100', // 音频采样率
      '-ac', '2', // 双声道
      '-movflags', '+faststart', // 优化网络播放
      '-brand', 'mp42', // 设置兼容的brand
      'output.mp4'
    )
    
    // 读取转换后的文件
    const data = ffmpeg.FS('readFile', 'output.mp4')
    
    // 创建Blob并生成URL
    const blob = new Blob([data.buffer], { type: 'video/mp4' })
    
    // 释放之前的URL
    if (convertedVideoUrl.value) {
      URL.revokeObjectURL(convertedVideoUrl.value)
    }
    
    // 创建新的URL
    convertedVideoUrl.value = URL.createObjectURL(blob)
    
    statusMessage.value = '转换完成!'
  } catch (error) {
    console.error('转换失败:', error)
    statusMessage.value = `转换失败: ${error.message}`
  } finally {
    isConverting.value = false
  }
}

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value)
  }
  if (convertedVideoUrl.value) {
    URL.revokeObjectURL(convertedVideoUrl.value)
  }
})
</script>

<style scoped>
.video-convert-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-size: 16px;
}

.compatibility-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #ffeeba;
}

.compatibility-warning ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 24px;
}

.upload-area {
  border: 2px dashed #ccc;
  padding: 30px;
  text-align: center;
  margin-bottom: 30px;
  border-radius: 8px;
}

.file-input {
  display: none;
}

.select-btn {
  background-color: #1969ff;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.file-info {
  margin-top: 15px;
  color: #666;
}

.analyze-section, .convert-section {
  margin-bottom: 20px;
}

.analyze-btn, .convert-btn {
  background-color: #1969ff;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
}

.convert-btn {
  background-color: #28a745;
}

.analyze-btn:disabled, .convert-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.progress-container {
  margin-bottom: 30px;
}

.progress-bar {
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #1969ff;
  transition: width 0.3s;
}

.progress-text {
  text-align: center;
  margin-bottom: 5px;
}

.status-text {
  text-align: center;
  color: #666;
}

.video-preview-container {
  margin-bottom: 30px;
  text-align: center;
}

.video-preview-container h2 {
  margin-bottom: 15px;
}

.download-section {
  margin-top: 15px;
}

.download-btn {
  display: inline-block;
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 16px;
}

.result-container {
  margin-top: 30px;
}

.info-card {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.info-label {
  font-weight: bold;
  width: 120px;
}

.info-value {
  flex: 1;
}

.good-compatibility {
  color: #28a745;
  font-weight: bold;
}

.poor-compatibility {
  color: #dc3545;
  font-weight: bold;
}

.raw-info {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  overflow: auto;
}

.raw-info pre {
  white-space: pre-wrap;
  font-family: monospace;
  margin: 0;
}
</style>


