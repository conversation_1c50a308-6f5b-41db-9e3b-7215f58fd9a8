import request from '@/utils/request'

// 查询用户答案列表
export function listAnswers(query) {
  return request({
    url: '/answers/list',
    method: 'get',
    params: query
  })
}

// 查询用户答案详细
export function getAnswers(id) {
  return request({
    url: '/answers/' + id,
    method: 'get'
  })
}

// 新增用户答案
export function addAnswers(data) {
  return request({
    url: '/answers',
    method: 'post',
    data: data
  })
}

// 修改用户答案
export function updateAnswers(data) {
  return request({
    url: '/answers',
    method: 'put',
    data: data
  })
}

// 删除用户答案
export function delAnswers(id) {
  return request({
    url: '/answers/' + id,
    method: 'delete'
  })
}
