<template>
  <div class="app-wrapper overflow-hidden">
    <video v-show="!standbyType" ref="videoEl" class="w-100vw h-100vh object-fill" playsinline></video>
    <video v-show="standbyType" ref="standbyVideo" class="w-100vw h-100vh object-fill" src="/src/assets/video/screen/standby.mp4" autoplay muted loop></video>

    <!-- v-if="['ScreenTestTopic', 'ScreenChat', 'ScreenPreIndex'].includes(route.name)" -->
    <video-record ref="recorder" @start="console.log('开始录制')" @success="handleSuccess" @error="handleError" @progress="handleProgress" />

    <!-- <button style="font-size: 50px" @click="start">开始录制</button>
    <button style="font-size: 50px" @click="stop">停止并上传</button> -->
    <section class="app-main">
      <router-view
        v-slot="{ Component, route }"
        @standbyVideoPlay="standbyVideoPlay"
        @startRecording="start"
        @stopRecording="stop"
        @handlePlay="play"
        @handlePause="pause"
      >
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="tagsViewStore.cachedViews">
            <component v-if="!route.meta.link" :is="Component" :key="route.path" />
          </keep-alive>
        </transition>
      </router-view>
    </section>
  </div>
</template>

<script setup>
import { provide } from 'vue'
import useTagsViewStore from '@/store/modules/tagsView'
import VideoRecord from './components/VideoRecord'
const modules = import.meta.glob('@/assets/video/screen/*.{mp4,m4v}', { eager: true }) // 引入视频文件

// #region 数字人播放
// 视频播放器封装逻辑
const videoEl = ref(null)
const standbyVideo = ref(null)
const state = reactive({
  currentSource: null,
  isLoop: false,
  isPlaying: false
})
const standbyType = ref(false)

// 初始化视频元素
const initVideo = () => {
  // if (!videoEl.value) return
  // videoEl.value.src = defaultSource
  // videoEl.value.load()
}
//播放人物无操作动态视频
const standbyVideoPlay = type => {
  if (type) {
    setTimeout(function () {
      standbyType.value = true
      standbyVideo.value.play()
    }, 100)
  } else {
    standbyType.value = false
    standbyVideo.value.pause()
  }
}

// 核心播放方法
const play = (
  source, // 新视频源地址（可选）
  options = {
    loop: false, // 循环模式
    muted: false, // 静音播放
    play: true, // 是否播放
    restart: true, // 是否从头开始
    onEnd: () => {
      standbyVideoPlay(true)
    }, // 结束回调
    onError: () => {} // 错误回调
  }
) => {
  if (!videoEl.value) return

  // 清理旧事件监听
  videoEl.value.removeEventListener('ended', handleVideoEnd)
  videoEl.value.removeEventListener('error', handleError)

  // 处理视频源变化
  source = modules[Object.keys(modules).find(key => key.includes(source))].default
  const shouldChangeSource = source && source !== state.currentSource
  if (shouldChangeSource) {
    state.currentSource = source
    videoEl.value.src = source
    videoEl.value.load()
  }

  // 配置播放参数
  state.isLoop = options.loop
  videoEl.value.muted = options.muted
  videoEl.value.loop = options.loop
  if (options.restart) videoEl.value.currentTime = 0
  if (!options.play) {
    // play为false时，强制暂停并重置到第一帧，不自动播放
    videoEl.value.pause()
    state.isPlaying = false
    return
  }

  // 事件监听
  videoEl.value.addEventListener('ended', handleVideoEnd)
  videoEl.value.addEventListener('error', handleError)

  // 执行播放
  const startPlay = () => {
    videoEl.value.play().catch(error => {
      standbyVideoPlay(true)
    })
    standbyVideoPlay(false)
    videoEl.value.play()
    state.isPlaying = true
    videoEl.value.removeEventListener('loadeddata', startPlay)
  }
  if (shouldChangeSource) {
    videoEl.value.addEventListener('loadeddata', startPlay)
  } else {
    startPlay()
  }

  // 错误处理
  function handleError(e) {
    state.isPlaying = false
    options.onError?.(e.target.error)
  }
  // 处理播放结束
  function handleVideoEnd() {
    if (!state.isLoop) {
      state.isPlaying = false
      videoEl.value.currentTime = 0
      options.onEnd?.()
    }
  }
}

// 暂停控制
const pause = (options = { reset: false }) => {
  if (!state.isPlaying) return

  if (options.reset) {
    videoEl.value.currentTime = 0
  }

  videoEl.value.pause()
  state.isPlaying = false
}

// 组件生命周期
onBeforeUnmount(() => {
  if (videoEl.value) {
    videoEl.value.pause()
    videoEl.value.removeAttribute('src')
    videoEl.value.load()
  }
})
// #endregion

// #region 视频录制
const recorder = ref(null)

// 提供视频录制组件的方法给子组件
provide('recorder', {
  start: () => recorder.value?.start(),
  stop: metadata => recorder.value?.stop(metadata),
  areAllUploadsComplete: () => recorder.value?.areAllUploadsComplete() || true,
  getUploadStatusSummary: () => recorder.value?.getUploadStatusSummary() || { total: 0, completed: 0, error: 0, inProgress: 0, progress: 100 }
})

const start = () => {
  console.log('layout 开始录制')
  if (recorder.value) {
    recorder.value.start()
  } else {
    console.error('recorder 组件未初始化')
  }
}

// 停止录制并创建上传任务
const stop = metadata => {
  if (recorder.value) {
    return recorder.value.stop(metadata)
  } else {
    console.error('recorder 组件未初始化')
    // 确保回调被调用，避免阻塞答题流程
    if (metadata && typeof metadata.callback === 'function') {
      metadata.callback({ url: `error_${metadata.topicId || 'unknown'}` })
    }
  }
}

const handleSuccess = response => {
  console.log('上传成功:', response)

  // 发布一个自定义事件，通知题目组件更新URL
  const customEvent = new CustomEvent('video-upload-success', {
    detail: {
      taskId: response.taskId,
      url: response.response.url,
      metadata: response.metadata
    }
  })
  window.dispatchEvent(customEvent)
}

const handleError = error => {
  console.error('发生错误:', error)
}

const handleProgress = percent => {
  console.log(`上传进度: ${percent}%`)
}
// #endregion
const route = useRoute()
const tagsViewStore = useTagsViewStore()

onMounted(() => {
  addIframe()
  // // 初始化默认视频源
  // initVideo()
})

watch(route => {
  addIframe()
})

function addIframe() {
  if (route.meta.link) {
    useTagsViewStore().addIframeView(route)
  }
}
</script>

<style>
/* message提示 */
.el-message {
  padding: 32px;
  border-radius: 16px;

  .el-icon {
    font-size: 60px;
  }

  .el-message__content {
    font-size: 46px;
  }
}
</style>
<style lang="scss" scoped>
.app-wrapper {
  height: 100vh;
  background-image: url(@/assets/images/screen/bg-wrapper.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;

  // 横屏时的处理
  @media screen and (orientation: landscape) {
    &::before {
      content: '请旋转设备至竖屏模式以获得最佳体验';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      z-index: 9999;
      padding: 20px;
      box-sizing: border-box;
      text-align: center;
    }
  }

  .app-main {
    width: 100%;
    min-height: 1000px;
    max-height: 50vh;
    position: absolute;
    left: 0px;
    right: 0px;
    top: 50%;

    :deep(.backdrop-blur) {
      min-height: 1000px;
      background: rgba(255, 255, 255, 0.34);
      backdrop-filter: blur(40px);

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 49%, rgba(255, 255, 255, 0) 100%);
        backdrop-filter: blur(41px);
      }

      &::before {
        top: 0;
      }

      &::after {
        bottom: 0;
      }
    }

    :deep(.screen-button) {
      --primary-color: #033682;

      & {
        box-shadow: 0 20px 20px 20px #00000004;
        transition: 0.3s;
        position: relative;
      }

      &::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        opacity: 0.4;
        transition: 0.3s;
        /*其他样式*/
        opacity: 0;
        box-shadow: 0 0 0 40px var(--primary-color);
        transition: 0.3s;
      }

      &:active::after {
        box-shadow: none;
        opacity: 0.4;
        /*取消过渡*/
        transition: 0s;
      }
    }
  }
}
</style>
