<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true">
            <el-form-item label="" prop="postCode">
                <el-input v-model="queryParams.examName" placeholder="请输入项目名称搜索" clearable style="width: 200px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="测评结果" prop="status">
                <el-select v-model="queryParams.scoreStatus" placeholder="请选择" clearable style="width: 200px">
                    <el-option label="正常" :value="0" />
                    <el-option label="异常" :value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="测评时间" prop="dateRange">
                <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD HH:mm:ss" type="datetimerange"
                    range-separator="-" start-placeholder="请选择时间" end-placeholder="请选择时间"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="postList">
            <el-table-column label="序号" align="center" type="index" width="180" />
            <el-table-column label="测评项目" align="center" prop="examName" />
            <el-table-column label="测评时间" align="center" prop="createTime" />
            <el-table-column label="测评用时" align="center">
                <template #default="scope">
                    <span>{{ proxy.calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="测评结果" align="center">
                <template #default="scope">
                    <el-tag v-if="scope.row.scoreStatus == 0" type="success">正常</el-tag>
                    <el-tag v-else type="danger">异常</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary"
                        @click="handleDetail(scope.row.examId, scope.row.id)">答题详情</el-button>
                    <el-button link type="primary"
                        @click="handleResult(scope.row.id, scope.row.userId)">测评报告</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup>
//  import { getPercentageList, getReportInfo } from '@/api/test/examsRecord'
import { listExamsRecord } from '@/api/test/examsRecord'

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")
const router = useRouter()
const route = useRoute()
const postList = ref([])
const dateRange = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        examName: '',
        startTime: undefined,
        endTime: undefined,
        scoreStatus: undefined
    }
})

const { queryParams, form } = toRefs(data)

/** 查询测评列表 */
function getList() {
    loading.value = true
    listExamsRecord(queryParams.value).then(response => {
        postList.value = response.rows
        total.value = response.total
        loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    if (dateRange.value.length > 0) {
        queryParams.value.startTime = dateRange.value[0]
        queryParams.value.endTime = dateRange.value[1]
    }
    getList()
}

/** 重置按钮操作 */
function resetQuery() {
    dateRange.value = []
    queryParams.value.startTime = undefined
    queryParams.value.endTime = undefined
    proxy.resetForm("queryRef")
    handleQuery()
}

/** 答题详情 */
function handleDetail(id, examId) {
    // console.log(id)
    router.push({ name: 'AssessListDetail', query: { id: id, examId: examId } })
}
/** 测评报告 */
function handleResult(id, userId) {
    // console.log(id)
    router.push({ name: 'AssessListResult', query: { id: id, userId: userId } })
}
getList();
</script>