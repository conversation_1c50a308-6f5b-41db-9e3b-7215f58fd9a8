package com.ruoyi.exams.service;

import com.ruoyi.exams.domain.AnswerVideos;

import java.util.List;

/**
 * 答题视频记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface IAnswerVideosService {

    /**
     * 查询答题视频记录
     *
     * @param id 答题视频记录主键
     * @return 答题视频记录
     */
    public AnswerVideos selectAnswerVideosById(Long id);

    /**
     * 查询答题视频记录列表
     *
     * @param answerVideos 答题视频记录
     * @return 答题视频记录集合
     */
    public List<AnswerVideos> selectAnswerVideosList(AnswerVideos answerVideos);

    /**
     * 新增答题视频记录
     *
     * @param answerVideos 答题视频记录
     * @return 结果
     */
    public int insertAnswerVideos(AnswerVideos answerVideos);

    /**
     * 修改答题视频记录
     *
     * @param answerVideos 答题视频记录
     * @return 结果
     */
    public int updateAnswerVideos(AnswerVideos answerVideos);

    /**
     * 批量删除答题视频记录
     *
     * @param ids 需要删除的答题视频记录主键集合
     * @return 结果
     */
    public int deleteAnswerVideosByIds(Long[] ids);

    /**
     * 删除答题视频记录信息
     *
     * @param id 答题视频记录主键
     * @return 结果
     */
    public int deleteAnswerVideosById(Long id);

}
