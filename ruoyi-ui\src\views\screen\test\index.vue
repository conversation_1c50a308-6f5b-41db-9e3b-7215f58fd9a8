<template>
  <div class="screen-container">
    <!-- 介绍语句 -->
    <div class="introduction">尊敬的用户您好，您想测评下列哪个项目？</div>
    <!-- 按钮 -->
    <div class="button-container flex justify-center">
      <div class="screen-button flex flex-col" @click="handleTest(item.id)" v-for="item in examsList">
        <p>{{ item.name }}</p>
        <span>{{ item.description }}</span>
      </div>
      <!-- <div class="screen-button flex flex-col" @click="handleTest('SES')">
        <p>抑郁自评</p>
        <span>SES</span>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import { listExams } from '@/api/test/exams'

const router = useRouter()
const examsList = ref([]) // 试卷列表

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  }
})

const { queryParams } = toRefs(data)

// #region 视频播放
const emit = defineEmits(['handlePlay'])
onMounted(() => {
  emit('handlePlay', 'prompt3')
})
// #endregion

/** 查询试卷列表 */
function getList() {
  listExams(queryParams.value).then(response => {
    examsList.value = response.rows
  })
}
getList()

const handleTest = params => {
  setTimeout(() => {
    router.push({ name: 'ScreenTestTopic', query: { id: params } })
  }, 300)
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  .introduction {
    padding: 0 218px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 110px;
    text-align: center;
    color: #033682;
  }
  // 按钮
  .button-container {
    margin-top: 124px;
    gap: 81px;
    .screen-button {
      gap: 32px;
      width: 620px;
      height: 680px;
      background-size: 100% 100%;
      border-radius: 72px;
      &:nth-child(1) {
        background-image: url(@/assets/images/screen/bg-test1.png);
      }
      &:nth-child(2) {
        background-image: url(@/assets/images/screen/bg-test2.png);
      }

      padding: 93px 80px;
      p {
        margin: 0;
        font-family: DingTalk JinBuTi;
        font-size: 90px;
        line-height: 90px;
        color: #ffffff;
      }
      span {
        font-family: DingTalk JinBuTi;
        font-size: 60px;
        line-height: 60px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}
</style>
