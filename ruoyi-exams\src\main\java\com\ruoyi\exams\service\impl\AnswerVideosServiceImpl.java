package com.ruoyi.exams.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.AnswerVideos;
import com.ruoyi.exams.mapper.AnswerVideosMapper;
import com.ruoyi.exams.service.IAnswerVideosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 答题视频记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class AnswerVideosServiceImpl implements IAnswerVideosService {

    @Autowired
    private AnswerVideosMapper answerVideosMapper;

    /**
     * 查询答题视频记录
     *
     * @param id 答题视频记录主键
     * @return 答题视频记录
     */
    @Override
    public AnswerVideos selectAnswerVideosById(Long id)
    {
        return answerVideosMapper.selectAnswerVideosById(id);
    }

    /**
     * 查询答题视频记录列表
     *
     * @param answerVideos 答题视频记录
     * @return 答题视频记录
     */
    @Override
    public List<AnswerVideos> selectAnswerVideosList(AnswerVideos answerVideos)
    {
        return answerVideosMapper.selectAnswerVideosList(answerVideos);
    }

    /**
     * 新增答题视频记录
     *
     * @param answerVideos 答题视频记录
     * @return 结果
     */
    @Override
    public int insertAnswerVideos(AnswerVideos answerVideos)
    {
        answerVideos.setCreateBy(SecurityUtils.getUserId().toString());
        answerVideos.setCreateTime(DateUtils.getNowDate());
        return answerVideosMapper.insertAnswerVideos(answerVideos);
    }

    /**
     * 修改答题视频记录
     *
     * @param answerVideos 答题视频记录
     * @return 结果
     */
    @Override
    public int updateAnswerVideos(AnswerVideos answerVideos)
    {
        answerVideos.setUpdateBy(SecurityUtils.getUserId().toString());
        answerVideos.setUpdateTime(DateUtils.getNowDate());
        return answerVideosMapper.updateAnswerVideos(answerVideos);
    }

    /**
     * 批量删除答题视频记录
     *
     * @param ids 需要删除的答题视频记录主键
     * @return 结果
     */
    @Override
    public int deleteAnswerVideosByIds(Long[] ids)
    {
        return answerVideosMapper.deleteAnswerVideosByIds(ids);
    }

    /**
     * 删除答题视频记录信息
     *
     * @param id 答题视频记录主键
     * @return 结果
     */
    @Override
    public int deleteAnswerVideosById(Long id)
    {
        return answerVideosMapper.deleteAnswerVideosById(id);
    }

}
