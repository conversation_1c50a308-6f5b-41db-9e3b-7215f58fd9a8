<template>
  <div class="screen-container">
    <!-- 介绍语句 -->
    <div class="introduction">您好，我是您的私人AI测评师小美，助您了解心理健康，请扫码或者输入用户名密码验证身份。</div>
    <!-- 按钮 -->
    <div class="button-container flex justify-center">
      <div class="screen-button flex justify-center items-center" @click="handleLogin('qrcode')">
        <img src="@/assets/images/screen/icon-scan_qr.png" />
        扫码
      </div>
      <div class="screen-button flex justify-center items-center" @click="handleLogin('username')">
        <img src="@/assets/images/screen/icon-user.png" />
        用户名
      </div>
    </div>
  </div>
</template>

<script setup>
const router = useRouter()

// #region 视频播放
const emit = defineEmits(['handlePlay'])
onMounted(() => {
  emit('handlePlay', 'introduction')
})
// #endregion

const handleLogin = params => {
  setTimeout(() => {
    router.push({ name: 'ScreenLogin', query: { type: params } })
  }, 300)
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  height: 100%;
  font-size: 20px;
  .introduction {
    padding: 0 148px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 120px;
    text-align: justify;
    color: #033682;
  }
  // 按钮
  .button-container {
    margin-top: 104px;
    gap: 103px;
    .screen-button {
      gap: 24px;
      width: 600px;
      height: 195px;
      font-size: 72px;
      font-weight: 600;
      color: #ffffff;
      background-image: url(@/assets/images/screen/bg-btn.png);
      background-size: 100% 100%;
      border-radius: 120px;

      img {
        width: 72px;
        height: 72px;
      }
    }
  }
}
</style>
