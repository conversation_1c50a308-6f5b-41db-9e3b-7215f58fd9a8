<template>
  <div class="screen-container">
    <!-- 底部控制区域 -->
    <div class="control-bar flex justify-center">
      <div class="screen-button bg-[#EAF3FF] text-[#005EFF]" @click="handleBack">返 回</div>
      <div class="screen-button bg-[#EAF3FF] text-[#005EFF]" @click="playMaterial(materials[0],0,true)">播放全部</div>
      <div class="screen-button bg-[#005EFF] text-[#FFFFFF]" @click="handleFinish">完成测试</div>
    </div>

    <!-- 介绍语句 -->
    <div class="introduction">情绪诱发测试将通过播放视频和图片来测试您的情绪反应，请做好准备</div>

    <!-- 视频播放区域 -->
    <div class="video-container" v-if="currentMaterial && currentMaterial.type === 'video' && !showMaterialList">
      <video
        ref="videoPlayer"
        class="video-player"
        :src="currentMaterial.url"
        autoplay
        @ended="handleVideoEnded"
        @error="handleVideoError"
        @play="handleVideoPlay"
      ></video>

      <!-- 播放控制按钮 -->
      <div class="play-control">
        <div class="screen-button bg-[#EAF3FF] text-[#005EFF]" @click="cancelPlay">取消播放</div>
      </div>
    </div>

    <!-- 图片展示区域 -->
    <div class="image-container" v-else-if="currentMaterial && currentMaterial.type === 'image' && !showMaterialList">
      <img :src="currentMaterial.url" alt="情绪诱发图片" class="image-display" />

      <!-- 播放控制按钮 -->
      <div class="play-control">
        <div class="screen-button bg-[#EAF3FF] text-[#005EFF]" @click="cancelPlay">取消播放</div>
      </div>
    </div>

    <!-- 素材列表 -->
    <div class="material-list-container" v-if="showMaterialList">
      <material-list :materials="[...materials, ...materials]" :title="'情绪诱发素材'" @select="selectMaterial" @play="playMaterial" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import MaterialList from './components/MaterialList.vue'
import VideoRecord from '@/layout/screen/components/VideoRecord/index.vue'
import microExpression from '@/assets/video/demo/micro-expression.m4v'

const router = useRouter()

// 视频和画布引用
const videoPlayer = ref(null)
const recorder = ref(null)

// 是否显示素材列表
const showMaterialList = ref(true)

// 当前播放的素材
const currentMaterial = ref(null)
const currentIndex = ref(0)

// 录制状态
const isRecording = ref(false)
const recordedVideos = ref([])
const currentRecordUrl = ref('')

// 示例素材列表 - 使用已有的资源
const materials = ref([
  {
    id: 1,
    name: '快乐情绪视频',
    type: 'video',
    url: microExpression, // 使用已有的视频资源
    thumbnail: '/src/assets/images/screen/bg-test1.png', // 使用已有的图片资源作为缩略图
    duration: 30,
    emotion: 'happy'
  },
  {
    id: 2,
    name: '悲伤情绪视频',
    type: 'video',
    url: microExpression, // 使用已有的视频资源
    thumbnail: '/src/assets/images/screen/bg-test2.png', // 使用已有的图片资源作为缩略图
    duration: 30,
    emotion: 'sad'
  },
  {
    id: 3,
    name: '愤怒情绪图片',
    type: 'image',
    url: '/src/assets/images/screen/bg-test1.png', // 使用已有的图片资源
    emotion: 'angry'
  },
  {
    id: 4,
    name: '恐惧情绪图片',
    type: 'image',
    url: '/src/assets/images/screen/bg-test2.png', // 使用已有的图片资源
    emotion: 'fear'
  }
])

// 选择素材
const selectMaterial = (material, index) => {
  // 如果正在录制，先停止录制
  if (isRecording.value) {
    stopRecording()
  }

  currentMaterial.value = material
  currentIndex.value = index
  showMaterialList.value = false

  // 如果是图片，设置定时器自动返回列表
  if (material.type === 'image') {
    // 开始录制
    startRecording()

    setTimeout(() => {
      // 停止录制
      stopRecording()
      showMaterialList.value = true
    }, 5000) // 5秒后返回列表
  }
}

// 播放素材
const playMaterial = (material, index, playAll = false) => {
  selectMaterial(material, index)
  // 如果是播放全部，记录状态以便自动播放下一个
  if (playAll) {
    // 可以在这里添加自动播放逻辑
  }
}

// 取消播放
const cancelPlay = () => {
  // 如果正在录制，先停止录制
  if (isRecording.value) {
    stopRecording()
  }

  // 如果是视频，暂停播放
  if (currentMaterial.value && currentMaterial.value.type === 'video' && videoPlayer.value) {
    videoPlayer.value.pause()
  }

  showMaterialList.value = true
}

// 处理视频播放开始
const handleVideoPlay = () => {
  // 开始录制
  startRecording()
}

// 处理视频结束
const handleVideoEnded = () => {
  // 停止录制
  stopRecording()
  showMaterialList.value = true
}

// 处理视频错误
const handleVideoError = error => {
  console.error('视频播放错误:', error)
  // 如果正在录制，停止录制
  if (isRecording.value) {
    stopRecording()
  }
  showMaterialList.value = true
}

// 开始录制
const startRecording = async () => {
  if (recorder.value) {
    try {
      await recorder.value.start()
      isRecording.value = true
      console.log('开始录制视频')
    } catch (error) {
      console.error('录制开始失败:', error)
    }
  }
}

// 停止录制
const stopRecording = async () => {
  if (recorder.value && isRecording.value) {
    try {
      await recorder.value.stop({
        callback: function (res) {
          console.log(res)
        }
      })
      isRecording.value = false
      console.log('停止录制视频')
    } catch (error) {
      console.error('录制停止失败:', error)
    }
  }
}

// 录制开始回调
const handleRecordStart = () => {
  console.log('录制已开始')
  isRecording.value = true
}

// 录制成功回调
const handleRecordSuccess = videoUrl => {
  console.log('录制成功，视频URL:', videoUrl)
  currentRecordUrl.value = videoUrl
  recordedVideos.value.push({
    url: videoUrl,
    material: currentMaterial.value,
    timestamp: new Date().toISOString()
  })
  isRecording.value = false
}

// 录制错误回调
const handleRecordError = error => {
  console.error('录制错误:', error)
  isRecording.value = false
}

// 录制进度回调
const handleRecordProgress = progress => {
  console.log('录制进度:', progress)
}

// 完成测试
const handleFinish = () => {
  // 如果正在录制，先停止录制
  if (isRecording.value) {
    stopRecording()
  }

  // 可以在这里处理所有录制的视频
  console.log('所有录制的视频:', recordedVideos.value)

  router.back()
}

// 返回
const handleBack = () => {
  // 如果正在录制，先停止录制
  if (isRecording.value) {
    stopRecording()
  }
  setTimeout(() => router.back(), 300)
}

// 组件挂载
onMounted(() => {
  // 默认选择第一个素材
  if (materials.value.length > 0) {
    currentMaterial.value = materials.value[0]
    currentIndex.value = 0
  }
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 如果正在录制，停止录制
  if (isRecording.value) {
    stopRecording()
  }
})
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  height: 50vh;
  display: flex;
  flex-direction: column;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  // 介绍语句
  .introduction {
    position: absolute;
    margin-top: -240px;
    padding: 0 148px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 120px;
    text-align: center;
    color: #033682;
  }
  // 按钮
  .control-bar {
    width: 100%;
    gap: 60px;
    padding: 40px;

    .screen-button {
      width: 540px;
      flex: 1;
      height: 160px;
      border-radius: 50px;
      box-sizing: border-box;
      border: 4px solid #ffffff;
      backdrop-filter: blur(153px);

      font-family: DingTalk JinBuTi;
      font-size: 60px;
      line-height: 160px;
      text-align: center;
    }
  }

  // 素材列表容器
  .material-list-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 视频播放区域
  .video-container,
  .image-container {
    position: absolute;
    margin-top: -50vh;
    width: 100%;
    height: calc(100vh - 300px);
    background-color: #000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .video-player,
    .image-display {
      max-width: 100%;
      max-height: calc(100% - 120px);
    }

    // 播放控制按钮
    .play-control {
      position: absolute;
      bottom: 20px;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;

      .screen-button {
        width: 400px;
        height: 100px;
        border-radius: 50px;
        box-sizing: border-box;
        border: 4px solid #ffffff;
        backdrop-filter: blur(153px);

        font-family: DingTalk JinBuTi;
        font-size: 50px;
        line-height: 100px;
        text-align: center;
      }
    }
  }
}
</style>
