package com.ruoyi.exams.mapper;

import com.ruoyi.exams.domain.Questions;

import java.util.List;

/**
 * 题目Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface QuestionsMapper {

    /**
     * 查询题目
     *
     * @param id 题目主键
     * @return 题目
     */
    public Questions selectQuestionsById(Long id);

    /**
     * 查询题目列表
     *
     * @param questions 题目
     * @return 题目集合
     */
    public List<Questions> selectQuestionsList(Questions questions);

    /**
     * 新增题目
     *
     * @param questions 题目
     * @return 结果
     */
    public int insertQuestions(Questions questions);

    /**
     * 修改题目
     *
     * @param questions 题目
     * @return 结果
     */
    public int updateQuestions(Questions questions);

    /**
     * 删除题目
     *
     * @param id 题目主键
     * @return 结果
     */
    public int deleteQuestionsById(Long id);

    /**
     * 批量删除题目
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuestionsByIds(Long[] ids);

}
