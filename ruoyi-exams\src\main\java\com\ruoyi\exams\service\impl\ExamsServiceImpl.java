package com.ruoyi.exams.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.Exams;
import com.ruoyi.exams.mapper.ExamsMapper;
import com.ruoyi.exams.service.IExamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 试卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Service
public class ExamsServiceImpl implements IExamsService {

    @Autowired
    private ExamsMapper examsMapper;

    /**
     * 查询试卷
     *
     * @param id 试卷主键
     * @return 试卷
     */
    @Override
    public Exams selectExamsById(Long id)
    {
        return examsMapper.selectExamsById(id);
    }

    /**
     * 查询试卷列表
     *
     * @param exams 试卷
     * @return 试卷
     */
    @Override
    public List<Exams> selectExamsList(Exams exams)
    {
        return examsMapper.selectExamsList(exams);
    }

    /**
     * 新增试卷
     *
     * @param exams 试卷
     * @return 结果
     */
    @Override
    public int insertExams(Exams exams)
    {
        exams.setCreateBy(SecurityUtils.getUserId().toString());
        exams.setCreateTime(DateUtils.getNowDate());
        return examsMapper.insertExams(exams);
    }

    /**
     * 修改试卷
     *
     * @param exams 试卷
     * @return 结果
     */
    @Override
    public int updateExams(Exams exams)
    {
        exams.setUpdateBy(SecurityUtils.getUserId().toString());
        exams.setUpdateTime(DateUtils.getNowDate());
        return examsMapper.updateExams(exams);
    }

    /**
     * 批量删除试卷
     *
     * @param ids 需要删除的试卷主键
     * @return 结果
     */
    @Override
    public int deleteExamsByIds(Long[] ids)
    {
        return examsMapper.deleteExamsByIds(ids);
    }

    /**
     * 删除试卷信息
     *
     * @param id 试卷主键
     * @return 结果
     */
    @Override
    public int deleteExamsById(Long id)
    {
        return examsMapper.deleteExamsById(id);
    }

}
