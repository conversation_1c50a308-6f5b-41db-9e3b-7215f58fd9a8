/**
 * 人脸检测模块
 * 提供人脸检测和关键点绘制功能
 */
import { ref, onBeforeUnmount } from 'vue'
import * as faceapi from 'face-api.js'

export function useFaceDetection() {
  // 人脸检测相关状态
  const faceDetected = ref(false) // 是否检测到人脸
  const faceDetecting = ref(false) // 是否正在检测
  let rafId = null // requestAnimationFrame id

  /**
   * 加载 face-api.js 人脸检测模型
   * @returns {Promise<void>}
   */
  async function loadFaceDetector() {
    try {
      // 加载模型权重（首次加载会有延迟）
      await faceapi.nets.tinyFaceDetector.loadFromUri((window.location.host.includes('chuangyitong') ? '/screen' : '') + '/models')
      await faceapi.nets.faceLandmark68TinyNet.loadFromUri((window.location.host.includes('chuangyitong') ? '/screen' : '') + '/models')
      await faceapi.nets.faceExpressionNet.loadFromUri((window.location.host.includes('chuangyitong') ? '/screen' : '') + '/models')
      console.log('face-api.js 模型已加载')
      return true
    } catch (err) {
      console.error('人脸检测模型加载失败:', err)
      return false
    }
  }

  /**
   * 启动人脸检测循环
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {HTMLCanvasElement} canvasElement - 画布元素
   */
  function startFaceDetectionLoop(videoElement, canvasElement) {
    if (!videoElement || !canvasElement) {
      console.error('视频或画布元素未提供')
      return
    }

    faceDetecting.value = true
    let frameCount = 0
    let lastDetections = []
    const ctx = canvasElement.getContext('2d', { willReadFrequently: true })

    async function detect() {
      if (!videoElement || !canvasElement || videoElement.readyState < 2) {
        rafId = requestAnimationFrame(detect)
        return
      }

      frameCount++
      // 每3帧检测一次，否则用上一次结果绘制
      if (frameCount % 3 === 0) {
        try {
          lastDetections = await faceapi
            .detectAllFaces(videoElement, new faceapi.TinyFaceDetectorOptions({ inputSize: 320, scoreThreshold: 0.5 }))
            .withFaceLandmarks(true)
            .withFaceExpressions()
        } catch (e) {
          lastDetections = []
        }
      }

      // 每帧都重绘canvas，保证画面流畅
      ctx.clearRect(0, 0, canvasElement.width, canvasElement.height)
      if (lastDetections.length > 0) {
        faceDetected.value = true
        lastDetections.forEach(det => {
          // 绘制人脸框
          const box = det.detection.box
          ctx.strokeStyle = '#1af117'
          ctx.lineWidth = 2
          ctx.strokeRect(box.x, box.y, box.width, box.height)

          // 绘制关键点
          ctx.fillStyle = '#1af117'
          det.landmarks.positions.forEach(pt => {
            ctx.beginPath()
            ctx.arc(pt.x, pt.y, 2, 0, 2 * Math.PI)
            ctx.fill()
          })

          // 检测表情并绘制在框附近
          const expr = det.expressions
          let max = 0,
            maxKey = ''
          for (const [k, v] of Object.entries(expr)) {
            if (v > max) {
              max = v
              maxKey = k
            }
          }

          // 表情英文转中文
          const expressionMap = { happy: '高兴', sad: '伤心', angry: '生气', surprised: '惊讶', disgusted: '厌恶', fearful: '害怕', neutral: '平静' }
          const label = expressionMap[maxKey] || ''

          // 在人脸框上方居中绘制中文表情
          if (label) {
            ctx.save()
            ctx.font = '42px sans-serif'
            // ctx.fillStyle = '#1969ff'
            ctx.fillStyle = '#1af117'
            ctx.textAlign = 'center'
            ctx.scale(-1, 1) // 镜像
            ctx.fillText(label, box.x + box.width / 2, Math.max(box.y - 10, 32))
            ctx.restore()
          }
        })
      } else {
        faceDetected.value = false
      }

      rafId = requestAnimationFrame(detect)
    }

    rafId = requestAnimationFrame(detect)
  }

  /**
   * 初始化视频和画布
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {HTMLCanvasElement} canvasElement - 画布元素
   */
  async function initFaceDetection(videoElement, canvasElement) {
    if (!videoElement || !canvasElement) {
      console.error('视频或画布元素未提供')
      return false
    }

    // 设置画布尺寸
    canvasElement.width = videoElement.videoWidth
    canvasElement.height = videoElement.videoHeight

    // 加载人脸检测模型
    const modelLoaded = await loadFaceDetector()
    if (!modelLoaded) return false

    // 启动人脸检测循环
    if (!faceDetecting.value) {
      startFaceDetectionLoop(videoElement, canvasElement)
    }

    return true
  }

  /**
   * 清理资源
   */
  function cleanup() {
    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
    faceDetecting.value = false
  }

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    faceDetected,
    faceDetecting,
    initFaceDetection,
    cleanup
  }
}
