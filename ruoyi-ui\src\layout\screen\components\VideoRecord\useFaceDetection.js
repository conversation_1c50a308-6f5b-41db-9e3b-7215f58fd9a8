/**
 * 人脸检测模块
 * 提供人脸检测和关键点绘制功能
 */
import { ref, onBeforeUnmount } from 'vue'
import * as faceapi from 'face-api.js'

export function useFaceDetection() {
  // 人脸检测相关状态
  const faceDetected = ref(false) // 是否检测到人脸
  const faceDetecting = ref(false) // 是否正在检测
  let rafId = null // requestAnimationFrame id

  /**
   * 加载 face-api.js 人脸检测模型
   * @returns {Promise<void>}
   */
  async function loadFaceDetector() {
    try {
      // 加载模型权重（首次加载会有延迟）
      await faceapi.nets.tinyFaceDetector.loadFromUri((window.location.host.includes('chuangyitong') ? '/screen' : '') + '/models')
      await faceapi.nets.faceLandmark68TinyNet.loadFromUri((window.location.host.includes('chuangyitong') ? '/screen' : '') + '/models')
      await faceapi.nets.faceExpressionNet.loadFromUri((window.location.host.includes('chuangyitong') ? '/screen' : '') + '/models')
      console.log('face-api.js 模型已加载')
      return true
    } catch (err) {
      console.error('人脸检测模型加载失败:', err)
      return false
    }
  }

  /**
   * 启动人脸检测循环
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {HTMLCanvasElement} canvasElement - 画布元素
   */
  function startFaceDetectionLoop(videoElement, canvasElement) {
    if (!videoElement || !canvasElement) {
      console.error('视频或画布元素未提供')
      return
    }

    faceDetecting.value = true
    let frameCount = 0
    let lastDetections = []
    const ctx = canvasElement.getContext('2d', { willReadFrequently: true })

    // 获取视频方向
    let isPortrait = canvasElement.dataset.orientation === 'portrait'
    console.log(`人脸检测循环启动，视频方向: ${isPortrait ? '竖屏' : '横屏'}`)

    // 缩放和偏移参数
    let scaleX = 1
    let scaleY = 1
    let offsetX = 0
    let offsetY = 0

    // 从Canvas的dataset中获取缩放和偏移信息
    if (canvasElement.dataset.scaleX) {
      scaleX = parseFloat(canvasElement.dataset.scaleX)
    }
    if (canvasElement.dataset.scaleY) {
      scaleY = parseFloat(canvasElement.dataset.scaleY)
    }
    if (canvasElement.dataset.offsetX) {
      offsetX = parseFloat(canvasElement.dataset.offsetX)
    }
    if (canvasElement.dataset.offsetY) {
      offsetY = parseFloat(canvasElement.dataset.offsetY)
    }

    // 监听Canvas大小变化事件
    const handleCanvasResize = event => {
      const { isPortrait: newIsPortrait, scaleX: newScaleX, scaleY: newScaleY, offsetX: newOffsetX, offsetY: newOffsetY } = event.detail

      isPortrait = newIsPortrait
      scaleX = newScaleX || 1
      scaleY = newScaleY || 1
      offsetX = newOffsetX || 0
      offsetY = newOffsetY || 0

      console.log(`Canvas大小变化: isPortrait=${isPortrait}, scaleX=${scaleX}, scaleY=${scaleY}, offsetX=${offsetX}, offsetY=${offsetY}`)
    }

    canvasElement.addEventListener('canvasResize', handleCanvasResize)

    async function detect() {
      if (!videoElement || !canvasElement || videoElement.readyState < 2) {
        rafId = requestAnimationFrame(detect)
        return
      }

      frameCount++
      // 每3帧检测一次，否则用上一次结果绘制
      if (frameCount % 3 === 0) {
        try {
          lastDetections = await faceapi
            .detectAllFaces(videoElement, new faceapi.TinyFaceDetectorOptions({ inputSize: 320, scoreThreshold: 0.5 }))
            .withFaceLandmarks(true)
            .withFaceExpressions()
        } catch (e) {
          lastDetections = []
        }
      }

      // 每帧都重绘canvas，保证画面流畅
      ctx.clearRect(0, 0, canvasElement.width, canvasElement.height)

      if (lastDetections.length > 0) {
        faceDetected.value = true

        // 保存当前上下文状态
        ctx.save()

        // 根据视频方向调整绘制
        if (isPortrait) {
          // 竖屏模式下，应用缩放和偏移
          // console.log('使用竖屏模式绘制人脸关键点，应用缩放和偏移')
        }

        lastDetections.forEach(det => {
          // 应用缩放和偏移到人脸框坐标
          const originalBox = det.detection.box
          const scaledBox = {
            x: originalBox.x * scaleX + offsetX,
            y: originalBox.y * scaleY + offsetY,
            width: originalBox.width * scaleX,
            height: originalBox.height * scaleY
          }

          // 绘制人脸框
          ctx.strokeStyle = '#1af117'
          ctx.lineWidth = 2
          ctx.strokeRect(scaledBox.x, scaledBox.y, scaledBox.width, scaledBox.height)

          // 绘制关键点
          ctx.fillStyle = '#1af117'
          det.landmarks.positions.forEach(pt => {
            // 应用缩放和偏移到关键点坐标
            const scaledX = pt.x * scaleX + offsetX
            const scaledY = pt.y * scaleY + offsetY

            ctx.beginPath()
            ctx.arc(scaledX, scaledY, 2, 0, 2 * Math.PI)
            ctx.fill()
          })

          // 检测表情并绘制在框附近
          const expr = det.expressions
          let max = 0,
            maxKey = ''
          for (const [k, v] of Object.entries(expr)) {
            if (v > max) {
              max = v
              maxKey = k
            }
          }

          // 表情英文转中文
          const expressionMap = { happy: '高兴', sad: '伤心', angry: '生气', surprised: '惊讶', disgusted: '厌恶', fearful: '害怕', neutral: '平静' }
          const label = expressionMap[maxKey] || ''

          // 在人脸框上方居中绘制中文表情
          if (label) {
            ctx.save()
            ctx.font = '42px sans-serif'
            ctx.fillStyle = '#1af117'
            ctx.textAlign = 'center'

            // 根据视频方向调整文本绘制
            ctx.scale(-1, 1) // 镜像文本
            ctx.fillText(label, -(scaledBox.x + scaledBox.width / 2), Math.max(scaledBox.y - 10, 32))

            ctx.restore()
          }
        })

        // 恢复上下文状态
        ctx.restore()
      } else {
        faceDetected.value = false
      }

      rafId = requestAnimationFrame(detect)
    }

    rafId = requestAnimationFrame(detect)

    // 返回清理函数，用于移除事件监听器
    return () => {
      canvasElement.removeEventListener('canvasResize', handleCanvasResize)
    }
  }

  /**
   * 初始化视频和画布
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {HTMLCanvasElement} canvasElement - 画布元素
   */
  async function initFaceDetection(videoElement, canvasElement) {
    if (!videoElement || !canvasElement) {
      console.error('视频或画布元素未提供')
      return false
    }

    // 设置画布尺寸
    canvasElement.width = videoElement.videoWidth
    canvasElement.height = videoElement.videoHeight

    // 检测视频流方向（竖屏还是横屏）
    const isPortrait = videoElement.videoHeight > videoElement.videoWidth
    console.log(`视频流方向: ${isPortrait ? '竖屏' : '横屏'}, 尺寸: ${videoElement.videoWidth}x${videoElement.videoHeight}`)

    // 将视频方向信息添加到canvas元素上，供CSS样式使用
    canvasElement.dataset.orientation = isPortrait ? 'portrait' : 'landscape'
    videoElement.dataset.orientation = isPortrait ? 'portrait' : 'landscape'

    // 从Canvas的dataset中获取缩放和偏移信息（如果存在）
    const scaleX = canvasElement.dataset.scaleX ? parseFloat(canvasElement.dataset.scaleX) : 1
    const scaleY = canvasElement.dataset.scaleY ? parseFloat(canvasElement.dataset.scaleY) : 1
    const offsetX = canvasElement.dataset.offsetX ? parseFloat(canvasElement.dataset.offsetX) : 0
    const offsetY = canvasElement.dataset.offsetY ? parseFloat(canvasElement.dataset.offsetY) : 0

    console.log(`初始化人脸检测，缩放和偏移: scaleX=${scaleX}, scaleY=${scaleY}, offsetX=${offsetX}, offsetY=${offsetY}`)

    // 加载人脸检测模型
    const modelLoaded = await loadFaceDetector()
    if (!modelLoaded) return false

    // 启动人脸检测循环
    if (!faceDetecting.value) {
      // 保存清理函数
      const cleanupListener = startFaceDetectionLoop(videoElement, canvasElement)

      // 在cleanup函数中调用清理函数
      const originalCleanup = cleanup
      cleanup = () => {
        originalCleanup()
        if (cleanupListener) cleanupListener()
      }
    }

    return true
  }

  /**
   * 清理资源
   */
  let cleanup = function () {
    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
    faceDetecting.value = false
  }

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    faceDetected,
    faceDetecting,
    initFaceDetection,
    cleanup
  }
}
