package com.ruoyi.exams.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.Options;
import com.ruoyi.exams.domain.Questions;
import com.ruoyi.exams.mapper.ExamsMapper;
import com.ruoyi.exams.mapper.OptionsMapper;
import com.ruoyi.exams.mapper.QuestionsMapper;
import com.ruoyi.exams.service.IOptionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 选项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class OptionsServiceImpl implements IOptionsService {

    @Autowired
    private OptionsMapper optionsMapper;

    @Autowired
    private ExamsMapper examsMapper;

    @Autowired
    private QuestionsMapper questionsMapper;

    /**
     * 查询选项
     *
     * @param id 选项主键
     * @return 选项
     */
    @Override
    public Options selectOptionsById(Long id)
    {
        return optionsMapper.selectOptionsById(id);
    }

    /**
     * 查询选项列表
     *
     * @param options 选项
     * @return 选项
     */
    @Override
    public List<Options> selectOptionsList(Options options)
    {
        return optionsMapper.selectOptionsList(options);
    }

    /**
     * 新增选项
     *
     * @param options 选项
     * @return 结果
     */
    @Override
    public int insertOptions(Options options)
    {
        options.setCreateBy(SecurityUtils.getUserId().toString());
        options.setCreateTime(DateUtils.getNowDate());
        return optionsMapper.insertOptions(options);
    }

    /**
     * 修改选项
     *
     * @param options 选项
     * @return 结果
     */
    @Override
    public int updateOptions(Options options)
    {
        options.setUpdateBy(SecurityUtils.getUserId().toString());
        options.setUpdateTime(DateUtils.getNowDate());
        return optionsMapper.updateOptions(options);
    }

    /**
     * 批量删除选项
     *
     * @param ids 需要删除的选项主键
     * @return 结果
     */
    @Override
    public int deleteOptionsByIds(Long[] ids)
    {
        return optionsMapper.deleteOptionsByIds(ids);
    }

    /**
     * 删除选项信息
     *
     * @param id 选项主键
     * @return 结果
     */
    @Override
    public int deleteOptionsById(Long id)
    {
        return optionsMapper.deleteOptionsById(id);
    }

    @Override
    public void batchInsertion() {
        String[] s = {"没有或几乎没有", "少有", "常有", "几乎一直有"};
        Questions questions = new Questions();
        questions.setExamId(1L);
        List<Questions> question = questionsMapper.selectQuestionsList(questions);
        for (Questions q : question) {
            Long id = q.getId();
            for (int i = 0; i < 4; i++) {
                Options options = new Options();
                options.setQuestionId(id);
                options.setOptionText(s[i]);
                String s1 = String.valueOf(i + 1);
                options.setSortOrder(Integer.parseInt(s1));
                options.setCreateBy(SecurityUtils.getUserId().toString());
                options.setCreateTime(new Date());
                optionsMapper.insertOptions(options);
            }
        }
    }

    @Override
    public void batchInsertion2() {
        String[] s = {"没有或很少有", "有时有", "大部分时间有（经常有）", "绝大多数时间有"};
        Questions questions = new Questions();
        questions.setExamId(2L);
        List<Questions> question = questionsMapper.selectQuestionsList(questions);
        for (Questions q : question) {
            Long id = q.getId();
            for (int i = 0; i < 4; i++) {
                Options options = new Options();
                options.setQuestionId(id);
                options.setOptionText(s[i]);
                String s1 = String.valueOf(i + 1);
                options.setSortOrder(Integer.parseInt(s1));
                options.setCreateBy(SecurityUtils.getUserId().toString());
                options.setCreateTime(new Date());
                optionsMapper.insertOptions(options);
            }
        }
    }

    @Override
    public Map queryTestQuestion(Long id) {
        List<Map<String, Object>> list = optionsMapper.queryTestQuestion(id);
        Map<Object, List<Map<String, Object>>> gather = list.stream()
                .collect(Collectors.groupingBy(
                        map -> map.get("questionId"),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        return gather;
    }

}
