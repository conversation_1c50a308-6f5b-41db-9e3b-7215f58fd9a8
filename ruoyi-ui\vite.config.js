import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'
import UnoCSS from 'unocss/vite'
import postCssPxToRem from 'postcss-pxtorem'
import copy from 'rollup-plugin-copy'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    // 允许导入 .m4v 视频文件作为静态资源
    assetsInclude: ['**/*.m4v'],
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === 'production' ? './' : '/',
    plugins: [
      ...createVitePlugins(env, command === 'build'),
      UnoCSS(),
      copy({
        targets: [
          { src: './node_modules/@ffmpeg/core/dist/ffmpeg-core.js', dest: process.env.NODE_ENV === 'production' ? 'dist/FFMPEG' : 'public/FFMPEG' },
          { src: './node_modules/@ffmpeg/core/dist/ffmpeg-core.wasm', dest: process.env.NODE_ENV === 'production' ? 'dist/FFMPEG' : 'public/FFMPEG' },
          { src: './node_modules/@ffmpeg/core/dist/ffmpeg-core.worker.js', dest: process.env.NODE_ENV === 'production' ? 'dist/FFMPEG' : 'public/FFMPEG' },
          // { src: './node_modules/@mediapipe/face_mesh', dest: process.env.NODE_ENV === 'production' ? 'dist' : 'public' }
        ],
        hook: process.env.NODE_ENV === 'production' ? 'writeBundle' : 'buildStart'
      })
    ],
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      // 目的是为了消除浏览器报错： SharedArrayBufferis not defined
      headers: { 'Cross-Origin-Opener-Policy': 'same-origin', 'Cross-Origin-Embedder-Policy': 'require-corp' },
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api/chat': {
          target: 'http://**********:8001',
          changeOrigin: true,
          rewrite: p => p.replace(/^\/dev-api\/chat/, '')
        },
        '/dev-api': {
          // target: 'http://localhost:8080', // 本地
          // target: 'http://************:8080', // 本地
          target: 'http://***********:7070', // 线上
          changeOrigin: true,
          rewrite: p => p.replace(/^\/dev-api/, '')
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          },
          postCssPxToRem({
            rootValue: 160,
            propList: ['*'],
            exclude: e => !e.includes('screen') // 仅转换大屏模块
          })
        ]
      }
    }
  }
})
