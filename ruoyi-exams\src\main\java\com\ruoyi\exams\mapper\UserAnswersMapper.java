package com.ruoyi.exams.mapper;

import com.ruoyi.exams.domain.UserAnswers;

import java.util.List;

/**
 * 用户答案Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface UserAnswersMapper {

    /**
     * 查询用户答案
     *
     * @param id 用户答案主键
     * @return 用户答案
     */
    public UserAnswers selectUserAnswersById(Long id);

    /**
     * 查询用户答案列表
     *
     * @param userAnswers 用户答案
     * @return 用户答案集合
     */
    public List<UserAnswers> selectUserAnswersList(UserAnswers userAnswers);

    /**
     * 新增用户答案
     *
     * @param userAnswers 用户答案
     * @return 结果
     */
    public int insertUserAnswers(UserAnswers userAnswers);

    /**
     * 修改用户答案
     *
     * @param userAnswers 用户答案
     * @return 结果
     */
    public int updateUserAnswers(UserAnswers userAnswers);

    /**
     * 删除用户答案
     *
     * @param id 用户答案主键
     * @return 结果
     */
    public int deleteUserAnswersById(Long id);

    /**
     * 批量删除用户答案
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserAnswersByIds(Long[] ids);

    public List<String> selectUrlList(Long userExamId);

}
