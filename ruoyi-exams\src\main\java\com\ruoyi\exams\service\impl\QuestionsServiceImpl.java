package com.ruoyi.exams.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.Questions;
import com.ruoyi.exams.mapper.QuestionsMapper;
import com.ruoyi.exams.service.IQuestionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 题目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Service
public class QuestionsServiceImpl implements IQuestionsService {

    @Autowired
    private QuestionsMapper questionsMapper;

    /**
     * 查询题目
     *
     * @param id 题目主键
     * @return 题目
     */
    @Override
    public Questions selectQuestionsById(Long id)
    {
        return questionsMapper.selectQuestionsById(id);
    }

    /**
     * 查询题目列表
     *
     * @param questions 题目
     * @return 题目
     */
    @Override
    public List<Questions> selectQuestionsList(Questions questions)
    {
        return questionsMapper.selectQuestionsList(questions);
    }

    /**
     * 新增题目
     *
     * @param questions 题目
     * @return 结果
     */
    @Override
    public int insertQuestions(Questions questions)
    {
        questions.setCreateBy(SecurityUtils.getUserId().toString());
        questions.setCreateTime(DateUtils.getNowDate());
        return questionsMapper.insertQuestions(questions);
    }

    /**
     * 修改题目
     *
     * @param questions 题目
     * @return 结果
     */
    @Override
    public int updateQuestions(Questions questions)
    {
        questions.setUpdateBy(SecurityUtils.getUserId().toString());
        questions.setUpdateTime(DateUtils.getNowDate());
        return questionsMapper.updateQuestions(questions);
    }

    /**
     * 批量删除题目
     *
     * @param ids 需要删除的题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionsByIds(Long[] ids)
    {
        return questionsMapper.deleteQuestionsByIds(ids);
    }

    /**
     * 删除题目信息
     *
     * @param id 题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionsById(Long id)
    {
        return questionsMapper.deleteQuestionsById(id);
    }
}
