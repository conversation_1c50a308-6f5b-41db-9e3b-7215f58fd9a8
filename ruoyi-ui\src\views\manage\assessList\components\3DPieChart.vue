<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import { reactive } from 'vue'

export default {
  setup() {
    const chartContainer = ref(null)
    let chartInstance = null
    const { proxy } = getCurrentInstance()
    
    // 原始数据配置
    const data = ref([
      { name: '积极', value: 35, itemStyle: { color: '#5d92ff' }, startRatio: 0, endRatio: 0.7 },
      { name: '消极', value: 32, itemStyle: { color: '#a25bff' }, startRatio: 0.7, endRatio: 1 },
      { name: '中性', value: 33, itemStyle: { color: '#48bdff' }, startRatio: 0.7, endRatio: 1 }
    ])

    // 获取3D饼图参数方程 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    const getParametricEquation = (startRatio, endRatio, isSelected, isHovered, k, h) => {
      // 计算
      let midRatio = (startRatio + endRatio) / 2
      let startRadian = startRatio * Math.PI * 2
      let endRadian = endRatio * Math.PI * 2
      let midRadian = midRatio * Math.PI * 2
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = true
      }
      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== 'undefined' ? k : 1 / 3
      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0
      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1
      // 返回曲面参数方程
      return {
        u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },
        v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
        x: function (u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        y: function (u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u)
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
        }
      }
    }

    // 生成3D饼图配置项
    const getPie3D = (pieData, internalDiameterRatio) => {
      //internalDiameterRatio:透明的空心占比
      let series = []
      let sumValue = 0
      let startValue = 0
      let endValue = 0
      let k = 1
      // pieData.sort((a, b) => {
      //   return b.value - a.value
      // })
      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value
        let seriesItem = {
          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
          type: 'surface',
          parametric: true,
          wireframe: { show: false },
          pieData: pieData[i],
          pieStatus: { selected: false, hovered: false, k: k },
          radius: '50%',
          center: ['10%', '10%']
        }

        if (typeof pieData[i].itemStyle != 'undefined') {
          let itemStyle = {}
          typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
          typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null
          seriesItem.itemStyle = itemStyle
        }
        series.push(seriesItem)
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。

      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value
        series[i].pieData.startRatio = startValue / sumValue
        series[i].pieData.endRatio = endValue / sumValue
        series[i].parametricEquation = getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio, false, false, k, series[i].pieData.value)
        startValue = endValue
      }
      let boxHeight = getHeight3D(series, 25) //通过传参设定3d饼/环的高度，26代表26px
      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        // backgroundColor: '#203598',
        labelLine: { show: true, lineStyle: { color: '#404772' } },
        label: {
          show: true,
          position: 'outside',
          rich: {
            b: { fontSize: proxy.getConvertedValue(20), lineHeight: proxy.getConvertedValue(30), color: '#606C8A', fontFamily: 'AlibabaPuHuiTiBold' },
            c: { fontSize: proxy.getConvertedValue(20), lineHeight: proxy.getConvertedValue(30), color: '#343847', fontFamily: 'Archivo Black' },
            d: { fontSize: proxy.getConvertedValue(20), lineHeight: proxy.getConvertedValue(30), color: '343847', fontFamily: 'AlibabaPuHuiTiBold' }
          },
          formatter: '{b|{b}}\n{c|{c}} {d|%}'
        },
        tooltip: {
          // backgroundColor: '#053A8D',
          formatter: params => {
            if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
              let bfb = ((option.series[params.seriesIndex].pieData.endRatio - option.series[params.seriesIndex].pieData.startRatio) * 100).toFixed(2)
              return (
                `${params.seriesName}<br/>` +
                `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:20px;height:20px;background-color:${params.color};"></span>` +
                `${bfb} %`
              )
            }
          }
        },
        xAxis3D: { min: -1, max: 1 },
        yAxis3D: { min: -1, max: 1 },
        zAxis3D: { min: -1, max: 1 },
        grid3D: {
          show: false,
          boxHeight: boxHeight, //圆环的高度
          left: 0,
          top: 0, //3d饼图的位置
          viewControl: {
            //3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 36, //角度
            distance: 340, //调整视角到主体的距离，类似调整zoom
            rotateSensitivity: 0, //设置为0无法旋转
            zoomSensitivity: 0, //设置为0无法缩放
            panSensitivity: 0, //设置为0无法平移
            autoRotate: false //自动旋转
          }
        },
        series: series
      }
      return option
    }

    // 获取饼图高度
    const getHeight3D = (series, height) => {
      // ...保持和原始代码相同的函数内容...
      series.sort((a, b) => {
        return b.pieData.value - a.pieData.value
      })
      return (height * 25) / series[0].pieData.value
    }

    // 初始化图表
    const initChart = () => {
      if (!chartContainer.value) return

      // 生成配置项
      const option = getPie3D(data.value, 0.8)

      // 添加2D引导线
      option.series.push({
        name: 'pie2d',
        type: 'pie',
        labelLine: {
          length: proxy.getConvertedValue(100),
          length2: proxy.getConvertedValue(40),
          lineStyle: { width: proxy.getConvertedValue(5), color: '#ffffff', cap: 'square', shadowColor: 'rgba(0, 0, 0, 0.5)', shadowBlur: 10 }
        },
        startAngle: -36,
        clockwise: false,
        radius: ['45%', '45%'],
        center: ['50%', '50%'],
        data: data.value,
        itemStyle: { opacity: 1 }
      })

      // 初始化实例
      chartInstance = echarts.init(chartContainer.value)
      chartInstance.setOption(option)

      // 窗口调整时自适应
      window.addEventListener('resize', handleResize)
    }

    // 处理窗口重置
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 生命周期
    onMounted(() => {
      initChart()
    })

    onBeforeUnmount(() => {
      if (chartInstance) {
        window.removeEventListener('resize', handleResize)
        chartInstance.dispose()
        chartInstance = null
      }
    })

    return { chartContainer ,data,initChart}
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 500px; /* 根据实际需求调整高度 */
  /* background-image: url('@/assets/images/screen/result/bg-3d_pie_chart.png');
  background-size: 527px 335px;
  background-repeat: no-repeat;
  background-position: center 188px; */
}
</style>
