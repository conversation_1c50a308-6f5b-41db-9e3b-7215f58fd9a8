<template>
  <div class="screen-container">
    <div class="introduction">测评历史</div>
    <div v-for="(item, index) in dateList" :key="index" class="screen-button flex flex-col gap-32px" @click="handleResult(item.id, item.examId)">
      <!-- <p>{{item.examName}}</p> -->
      <p>健康心理测试</p>
      <p class="dateText">{{ item.startTime }}</p>
      <span>点击查看</span>
    </div>
    <pagination
      v-show="pageTotal > 0"
      :total="pageTotal"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      layout="prev, pager, next, jumper"
    />
  </div>
</template>

<script setup>
import useUserStore from '@/store/modules/user'
import { listExamsRecord } from '@/api/test/examsRecord'
const router = useRouter()
const handleResult = (id, examId) => {
  setTimeout(() => {
    router.push({ name: 'ScreenResult', query: { id: id, examId: examId } })
  }, 30)
}
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userId: useUserStore().id
})
const dateList = ref([])
const pageTotal = ref(0)
const getList = () => {
  listExamsRecord(queryParams.value).then(response => {
    // console.log(response, 7878)
    dateList.value = response.rows
    pageTotal.value = response.total
  })
}
getList()
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  padding-bottom: 50px;

  // height: 100vh;
  background-image: url(@/assets/images/screen/bg-wrapper.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .introduction {
    padding: 110px 218px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 110px;
    text-align: center;
    color: #1969ff;
  }

  .screen-button {
    gap: 32px;
    // width: 1620px;
    height: 450px;
    background-size: 100% 100%;

    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);

    box-sizing: border-box;
    border: 4px solid #ffffff;

    backdrop-filter: blur(153px);

    border-radius: 50px;
    padding: 93px 80px;
    margin: 60px;

    p {
      margin: 0;
      font-family: DingTalk JinBuTi;
      font-size: 90px;
      line-height: 90px;
      color: #1969ff;
    }

    .dateText {
      font-size: 76px;
      font-family: DingTalk JinBuTi;
      line-height: 80px;
      color: #1969ff;
    }

    span {
      font-family: DingTalk JinBuTi;
      font-size: 60px;
      line-height: 60px;
      color: rgba($color: #1969ff, $alpha: 0.8);
    }
  }
}

// ::v-deep .el-pagination__jump{
//   font-size: 50px !important;
// }
</style>
