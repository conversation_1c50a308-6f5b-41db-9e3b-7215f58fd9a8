/**
 * 视频处理模块
 * 提供视频处理和转码功能
 */
import { ref, onMounted } from 'vue'
import { ffmpeg, fetchFile } from '@/utils/ffmpeg'

export function useVideoProcessing() {
  // 加载状态
  const isFFmpegLoaded = ref(false)
  const isTruncated = ref(false)

  // 初始化FFmpeg
  onMounted(async () => {
    try {
      if (!ffmpeg.isLoaded()) {
        await ffmpeg.load()
        isFFmpegLoaded.value = true
        console.log('FFmpeg 已加载')
      } else {
        isFFmpegLoaded.value = true
      }
    } catch (error) {
      console.error('FFmpeg 加载失败:', error)
    }
  })

  /**
   * 上传处理逻辑 - 截取视频最后10秒
   * @param {Blob} blob - 原始视频Blob
   * @returns {Promise<Blob>} 处理后的视频Blob
   */
  const processForUpload = async blob => {
    if (!isFFmpegLoaded.value) {
      throw new Error('视频处理器未就绪')
    }

    // 检查Blob是否有效
    if (!blob || blob.size === 0) {
      console.error('无效的视频Blob，大小为0');
      throw new Error('无效的视频数据');
    }

    console.log(`处理视频上传，Blob大小: ${blob.size} 字节, 类型: ${blob.type}`);

    try {
      // 1. 将原始Blob写入虚拟文件系统
      ffmpeg.FS('writeFile', 'input.webm', await fetchFile(blob))

      // 2. 执行关键帧分析并修复可能的问题
      console.log('执行视频分析和修复...');
      await ffmpeg.run(
        '-i', 'input.webm',
        '-c', 'copy',
        '-f', 'webm',
        '-map_metadata', '0',
        '-ignore_unknown',
        'analyzed.webm'
      )

      // 3. 获取视频时长
      const duration = await getVideoDuration(blob)
      console.log('获取到视频时长:', duration, '秒');

      // 4. 计算截取区间（最后10秒）
      const startTime = Math.max(0, duration - 10)
      isTruncated.value = startTime > 0
      console.log('视频截取起始点:', startTime, '秒');

      // 5. 对于短视频（小于1秒），使用特殊处理
      if (duration < 1) {
        console.log('检测到非常短的视频，使用特殊处理');
        // 对于极短视频，直接复制而不尝试截取
        await ffmpeg.run(
          '-i', 'input.webm',
          '-c:v', 'libx264', // 使用H.264编码
          '-profile:v', 'baseline', // 使用Baseline Profile（最大兼容性）
          '-preset', 'ultrafast', // 最快速度编码
          '-pix_fmt', 'yuv420p', // 确保像素格式兼容
          '-r', '30', // 固定帧率
          '-movflags', '+faststart', // 优化网络播放
          'output.mp4'
        )
      } else {
        // 6. 执行精准截取
        console.log('执行视频截取...');
        await ffmpeg.run(
          '-ss', startTime.toString(),
          '-i', 'input.webm',
          '-t', '10', // 截取10秒
          '-c:v', 'libx264', // 使用H.264编码而不是简单复制，提高兼容性
          '-profile:v', 'baseline',
          '-preset', 'ultrafast',
          '-pix_fmt', 'yuv420p',
          '-r', '30',
          '-avoid_negative_ts', '1',
          '-movflags', '+faststart',
          'output.mp4'
        )
      }

      // 7. 读取结果文件
      const data = ffmpeg.FS('readFile', 'output.mp4')
      const processedBlob = new Blob([data.buffer], { type: 'video/mp4' })
      console.log(`处理后的视频Blob大小: ${processedBlob.size} 字节`);

      // 8. 检查处理后的Blob是否有效
      if (processedBlob.size === 0) {
        console.error('处理后的视频大小为0，使用原始视频');
        // 如果处理失败，尝试直接转换原始视频
        await ffmpeg.run(
          '-i', 'input.webm',
          '-c:v', 'libx264',
          '-profile:v', 'baseline',
          '-preset', 'ultrafast',
          '-pix_fmt', 'yuv420p',
          'fallback.mp4'
        )

        const fallbackData = ffmpeg.FS('readFile', 'fallback.mp4')
        return new Blob([fallbackData.buffer], { type: 'video/mp4' })
      }

      return processedBlob
    } catch (error) {
      console.error('视频处理失败:', error)

      // 尝试应急处理 - 如果常规处理失败，尝试最简单的转换
      try {
        console.log('尝试应急处理...');
        await ffmpeg.run(
          '-i', 'input.webm',
          '-c:v', 'libx264',
          '-preset', 'ultrafast',
          'emergency.mp4'
        )

        const emergencyData = ffmpeg.FS('readFile', 'emergency.mp4')
        const emergencyBlob = new Blob([emergencyData.buffer], { type: 'video/mp4' })

        if (emergencyBlob.size > 0) {
          console.log('应急处理成功');
          return emergencyBlob
        }
      } catch (emergencyError) {
        console.error('应急处理也失败:', emergencyError)
      }

      throw error
    }
  }

  /**
   * 获取视频时长
   * @param {Blob} blob - 视频Blob
   * @returns {Promise<number>} 视频时长（秒）
   */
  const getVideoDuration = blob => {
    return new Promise(resolve => {
      // 检查Blob是否有效
      if (!blob || blob.size === 0) {
        console.error('无效的视频Blob，大小为0');
        // 对于无效Blob，返回一个默认时长而不是失败
        // 这样可以避免后续处理中断
        return resolve(1);
      }

      console.log(`尝试获取视频时长，Blob大小: ${blob.size} 字节, 类型: ${blob.type}`);

      const video = document.createElement('video')
      let timeoutId = null
      let hasResolved = false

      // 创建新的Object URL（避免地址复用问题）
      const objectUrl = URL.createObjectURL(blob)

      // 清理资源
      const cleanup = () => {
        // 先移除监听器再清除资源
        video.removeEventListener('loadedmetadata', onLoaded)
        video.removeEventListener('loadeddata', onLoadedData)
        video.removeEventListener('canplay', onCanPlay)
        video.removeEventListener('error', onError)
        URL.revokeObjectURL(objectUrl)
        clearTimeout(timeoutId)
        video.src = ''
      }

      // 成功回调 - 元数据加载完成
      const onLoaded = () => {
        if (hasResolved) return

        // 保留对象引用用于取值
        const duration = video.duration
        console.log('元数据加载完成，Raw duration:', duration)

        // 如果获取到有效时长，立即解析
        if (typeof duration === 'number' && !isNaN(duration) && duration > 0) {
          hasResolved = true
          setTimeout(() => cleanup(), 0)
          resolve(duration)
        }
        // 否则等待更多数据加载
      }

      // 数据加载完成回调
      const onLoadedData = () => {
        if (hasResolved) return

        const duration = video.duration
        console.log('数据加载完成，duration:', duration)

        if (typeof duration === 'number' && !isNaN(duration) && duration > 0) {
          hasResolved = true
          setTimeout(() => cleanup(), 0)
          resolve(duration)
        }
      }

      // 可以播放回调
      const onCanPlay = () => {
        if (hasResolved) return

        const duration = video.duration
        console.log('视频可以播放，duration:', duration)

        hasResolved = true
        setTimeout(() => cleanup(), 0)

        // 即使此时duration无效，也返回一个默认值
        if (typeof duration !== 'number' || isNaN(duration) || duration <= 0) {
          console.warn('无法获取有效时长，使用默认值');
          resolve(Math.max(1, blob.size / 100000)); // 根据文件大小估算时长
        } else {
          resolve(duration)
        }
      }

      // 错误回调
      const onError = event => {
        if (hasResolved) return
        console.error('Video error:', event.target.error)

        hasResolved = true
        cleanup()

        // 对于短视频，错误时返回一个默认时长而不是失败
        console.warn('视频加载错误，使用默认时长');
        resolve(Math.max(1, blob.size / 100000)); // 根据文件大小估算时长
      }

      // 超时处理（根据视频大小动态调整）
      const dynamicTimeout = Math.max(
        2000, // 最小2秒
        Math.min(blob.size / 512, 8000) // 每512字节增加1ms，最多8秒
      )

      // 设置双重超时保障
      timeoutId = setTimeout(() => {
        if (!hasResolved) {
          console.warn(`元数据加载超时 (${dynamicTimeout}ms)，使用默认时长`);
          hasResolved = true
          cleanup()
          // 超时时返回一个默认时长而不是失败
          resolve(Math.max(1, blob.size / 100000)); // 根据文件大小估算时长
        }
      }, dynamicTimeout)

      // 绑定多个事件监听，增加成功率
      video.addEventListener('loadedmetadata', onLoaded, { once: false })
      video.addEventListener('loadeddata', onLoadedData, { once: false })
      video.addEventListener('canplay', onCanPlay, { once: true })
      video.addEventListener('error', onError, { once: true })

      try {
        // 元数据加载配置
        video.preload = 'auto' // 改为auto以加载更多数据
        video.muted = true // 静音可以避免一些浏览器的自动播放限制
        video.playsInline = true // 移动端内联播放
        video.src = objectUrl

        // 尝试加载和播放
        video.load()
        video.play().catch(err => {
          console.warn('视频自动播放失败:', err)
          // 播放失败不影响元数据加载
        })
      } catch (err) {
        console.error('视频初始化错误:', err)
        if (!hasResolved) {
          hasResolved = true
          cleanup()
          // 初始化错误时返回一个默认时长而不是失败
          resolve(Math.max(1, blob.size / 100000)); // 根据文件大小估算时长
        }
      }
    })
  }

  /**
   * 下载处理方法
   * @param {Blob} blob - 视频Blob
   * @param {Object} metadata - 元数据
   * @returns {Promise<void>}
   */
  const downloadVideo = async (blob, metadata = {}) => {
    return new Promise(resolve => {
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = metadata.fileName || `recording_${Date.now()}.${blob.type.split('/')[1]}`
      document.body.appendChild(a)
      a.click()

      setTimeout(() => {
        URL.revokeObjectURL(url)
        a.remove()
        resolve()
      }, 100)
    })
  }

  return {
    isFFmpegLoaded,
    isTruncated,
    processForUpload,
    downloadVideo
  }
}
