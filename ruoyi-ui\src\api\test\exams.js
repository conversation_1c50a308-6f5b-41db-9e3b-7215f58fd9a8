import request from '@/utils/request'

// 查询试卷列表
export function listExams(query) {
  return request({
    url: '/exams/list',
    method: 'get',
    params: query
  })
}

// 查询试卷详细
export function getExams(id) {
  return request({
    url: '/exams/' + id,
    method: 'get'
  })
}

// 新增试卷
export function addExams(data) {
  return request({
    url: '/exams',
    method: 'post',
    data: data
  })
}

// 修改试卷
export function updateExams(data) {
  return request({
    url: '/exams',
    method: 'put',
    data: data
  })
}

// 删除试卷
export function delExams(id) {
  return request({
    url: '/exams/' + id,
    method: 'delete'
  })
}
