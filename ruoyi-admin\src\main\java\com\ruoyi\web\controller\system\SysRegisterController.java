package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysRegisterService;
import com.ruoyi.system.service.ISysConfigService;

/**
 * 注册验证
 *
 * <AUTHOR>
 */
@RestController
public class SysRegisterController extends BaseController
{
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    @PostMapping("/register")
    public AjaxResult register(@RequestBody RegisterBody user)
    {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }

        String uuid = user.getQrCode();
        String status = redisCache.getCacheObject("qrCode:" + uuid);

        if (status == null) {
            return AjaxResult.error("二维码已过期");
        }
        if (!"unused".equals(status)) {
            return AjaxResult.error("二维码已被使用");
        }

        String msg = registerService.register(user);
        redisCache.setCacheObject("qrCode:" + uuid, "used");

        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }
}
