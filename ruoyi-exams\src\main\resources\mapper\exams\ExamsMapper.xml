<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.ExamsMapper">

    <resultMap type="Exams" id="ExamsResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="timeLimit"    column="time_limit"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectExamsVo">
        select id, name, description, time_limit, status, create_by, create_time, update_by, update_time from exams
    </sql>

    <select id="selectExamsList" parameterType="Exams" resultMap="ExamsResult">
        <include refid="selectExamsVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="timeLimit != null "> and time_limit = #{timeLimit}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectExamsById" parameterType="Long" resultMap="ExamsResult">
        <include refid="selectExamsVo"/>
        where id = #{id}
    </select>

    <insert id="insertExams" parameterType="Exams" useGeneratedKeys="true" keyProperty="id">
        insert into exams
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="timeLimit != null">time_limit,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="timeLimit != null">#{timeLimit},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateExams" parameterType="Exams">
        update exams
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="timeLimit != null">time_limit = #{timeLimit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExamsById" parameterType="Long">
        delete from exams where id = #{id}
    </delete>

    <delete id="deleteExamsByIds" parameterType="String">
        delete from exams where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
