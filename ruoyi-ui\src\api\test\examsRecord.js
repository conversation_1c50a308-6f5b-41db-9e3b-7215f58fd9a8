import request from '@/utils/request'

// 查询用户考试记录列表
export function listExamsRecord(query) {
  return request({
    url: '/userExams/historyList',///userExams/historyList
    method: 'get',
    params: query
  })
}

// 查询用户考试记录详细
export function getExamsRecord(id) {
  return request({
    url: '/userExams/' + id,
    method: 'get'
  })
}
// 查询用户测评报告详情
export function getReportInfo(query) {
  return request({
    url: '/report/getReportInfo',
    method: 'get',
    params: query
  })
}
// 报告详情饼图和列表
export function getPercentageList(query) {
  return request({
    url: '/userExams/percentageList',
    method: 'get',
    params: query
  })
}
// 新增用户考试记录
export function addExamsRecord(data) {
  return request({
    url: '/userExams',
    method: 'post',
    data: data
  })
}

// 修改用户考试记录
export function updateExamsRecord(data) {
  return request({
    url: '/userExams',
    method: 'put',
    data: data
  })
}

// 删除用户考试记录
export function delExamsRecord(id) {
  return request({
    url: '/userExams/' + id,
    method: 'delete'
  })
}

// 获取用户考试答题详情
export function getTopicDetail(query) {
  return request({
    url: '/userExams/queryEvaluationRecord',
    method: 'get',
    params: query
  })
}