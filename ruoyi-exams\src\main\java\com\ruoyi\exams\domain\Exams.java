package com.ruoyi.exams.domain;

import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 试卷对象 exams
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
public class Exams extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 考试时长 */
    @Excel(name = "考试时长")
    private Integer timeLimit;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

}
