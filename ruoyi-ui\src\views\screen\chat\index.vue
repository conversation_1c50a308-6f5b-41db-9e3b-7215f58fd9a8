<template>
  <div class="screen-container">
    <div class="message-list" ref="messageListRef">
      <div v-for="(message, index) in messages" :key="index" class="message">
        <!-- 用户消息 -->
        <div class="user-message">
          <div class="message-content">{{ message.type === 'user'?message.content:'' }}</div>
        </div>
        <!-- 模型消息 -->
        <div class="model-message">
          <div ref="messageValue" class="message-content"></div>
        </div>
      </div>
    </div>
    <!-- 录音按钮和处理状态 -->
    <div class="record-btn-container">
      <button :disabled="isLoading" @click="handleRecord" class="record-btn">
        <!-- <img :src="isRecording ? iconStop : iconRecord" alt=""> -->
        <span v-if="!isRecording && !isLoading">🎤 开始录音</span>
        <span v-else-if="isRecording && !isLoading">结束录音（{{ remainSeconds }}s）</span>
        <span v-else>处理中...</span>
      </button>
    </div>
    <!-- <div class="finish-btn" @click="router.push({ name: 'ScreenTestFinish' })">结束</div> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { processMp3 } from '@/api/screen/chat'
import iconRecord from '@/assets/images/screen/chat/icon-record.png'
import iconStop from '@/assets/images/screen/chat/icon-stop.png'
// 引入 ffmpeg.wasm
// 全局唯一 ffmpeg 实例，避免多次加载和内存溢出
import { ffmpeg, fetchFile } from '@/utils/ffmpeg'

const router = useRouter()

const messages = ref([])

// 录音相关状态
const isRecording = ref(false) // 是否正在录音
const isLoading = ref(false) // 是否正在处理
const recorder = ref(null) // MediaRecorder 实例
const audioChunks = ref([]) // 录音数据块
const timer = ref(null) // 录音定时器
const remainSeconds = ref(60) // 剩余录音秒数
const textResult = ref('') // 语音转文本
const generatedText = ref('') // 大模型回复
const mp3Url = ref('') // AI回复语音的URL
const messageValue = ref([])//AI回复内容ref列表
const messageListRef=ref(null)//消息内容盒子元素

// #region 视频播放
const emit = defineEmits(['handlePlay', 'standbyVideoPlay'])
onMounted(() => {
  emit('handlePlay', 'chat', { restart: true, play: false })
  emit('standbyVideoPlay', true)
})
// #endregion

// 录音按钮点击事件
const handleRecord = () => {
  if (!isRecording.value) {
    startRecord()
  } else {
    stopRecord()
  }
}

// 开始录音
const startRecord = async () => {
  // 权限检测
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    alert('当前浏览器不支持音频录制')
    return
  }
  textResult.value = ''
  generatedText.value = ''
  mp3Url.value = ''
  audioChunks.value = []
  remainSeconds.value = 60
  isRecording.value = true
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    const mediaRecorder = new window.MediaRecorder(stream)
    recorder.value = mediaRecorder
    mediaRecorder.start()
    // 数据采集
    mediaRecorder.ondataavailable = e => {
      if (e.data.size > 0) {
        audioChunks.value.push(e.data)
      }
    }
    // 自动停止录音（60秒）
    timer.value = setInterval(() => {
      remainSeconds.value--
      if (remainSeconds.value <= 0) {
        stopRecord()
      }
    }, 1000)
  } catch (err) {
    isRecording.value = false
    alert('无法录音: ' + err)
  }
}

// 停止录音
const stopRecord = () => {
  if (!recorder.value) return
  isRecording.value = false
  clearInterval(timer.value)
  recorder.value.stop()
  recorder.value.onstop = async () => {
    // 合成音频文件
    const audioBlob = new Blob(audioChunks.value, { type: 'audio/webm' })
    // 1. 使用 ffmpeg.wasm 转码 webm 为 mp3
    const mp3Blob = await convertWebmToMp3(audioBlob)
    // 2. 上传 mp3 文件到后端
    await sendAudio(mp3Blob)
    // 释放麦克风
    recorder.value.stream.getTracks().forEach(track => track.stop())
    recorder.value = null
  }
}

// 使用 ffmpeg.wasm 将 webm 转为 mp3
const convertWebmToMp3 = async webmBlob => {
  // 首次调用需要加载 ffmpeg.wasm
  if (!ffmpeg.isLoaded()) {
    await ffmpeg.load()
  }
  // 写入虚拟文件系统
  ffmpeg.FS('writeFile', 'input.webm', await fetchFile(webmBlob))
  // 执行转码命令
  await ffmpeg.run('-i', 'input.webm', 'output.mp3')
  // 读取转码后的 mp3 文件
  const mp3Data = ffmpeg.FS('readFile', 'output.mp3')
  // 转为 Blob
  return new Blob([mp3Data.buffer], { type: 'audio/mp3' })
}

// 发送音频文件到后端（此处 audioBlob 已为 mp3 格式）
const sendAudio = async audioBlob => {
  isLoading.value = true
  try {
    const formData = new FormData()
    formData.append('file', audioBlob, 'record.mp3') // 指定 mp3 文件名
    // 调用后端接口
    const res = await processMp3(formData)

    // 解析后端返回
    // 语音转文本
    if (res.text_result) {
      // 后端可能返回多个 JSON 串拼接，如: {\n  "text" : "你好"\n}{\n  "text" : ""\n}
      // 用正则批量提取所有 "text"
      const matches = res.text_result.match(/\{[^}]*\}/g)
      let allText = ''
      if (matches) {
        // 遍历每个 JSON 字符串，提取 text 字段
        const textArr = matches
          .map(str => {
            try {
              const obj = JSON.parse(str)
              return obj.text || ''
            } catch {
              return ''
            }
          })
          .filter(t => t && t.trim() !== '')
        // 去除所有空格（包括中文、英文、全角、半角）
        allText = textArr.join('').replace(/\s+/g, '')
      }
      // 如果所有 text 字段都为空，则 textResult.value 设为 ''
      textResult.value = allText
      if (textResult.value) {
        messages.value.push({ content: textResult.value, type: 'user' })
      }
    }
    // 大模型文本回复
    if (res.generated_text) {
      generatedText.value = res.generated_text
      messages.value.push({ content: generatedText.value, type: 'ai' })
      typewriterEffect(messages.value.length - 1, generatedText.value, 180)
    }
    // AI回复语音
    if (res.mp3_base64) {
      mp3Url.value = 'data:audio/mp3;base64,' + res.mp3_base64

      // 自动播放MP3，不展示控件
      // 先移除旧audio防止多次触发
      let audio = document.getElementById('ai-audio')
      if (audio) {
        audio.pause()
        audio.currentTime = 0
        audio.remove()
      }
      // 创建新的audio标签
      audio = document.createElement('audio')
      audio.id = 'ai-audio'
      audio.src = mp3Url.value
      audio.autoplay = true
      audio.style.display = 'none' // 不展示控件
      document.body.appendChild(audio)

      // 播放mp3时，视频静音且暂停（play:false）
      emit('handlePlay', 'chat', { muted: true, loop: true, play: true })
      // MP3播放结束后，视频回到第一帧
      audio.onended = () => {
        emit('handlePlay', 'chat', { restart: true, play: false })
        emit('standbyVideoPlay', true)
      }
    }
  } catch (err) {
    alert('发送音频失败: ' + err)
  } finally {
    isLoading.value = false
  }
}

/**
 * 实现打字机效果
 * @param valueIndex 所选对象的下标
 * @param content 要展示的完整一段话内容
 * @param typeIntervalTime 间隔多久打出一个字（毫秒）
 */
const typewriterEffect = async (valueIndex, content, typeIntervalTime) => {
  let index = 0
  let temp = null
  temp = setInterval(() => {
      messageValue.value[valueIndex].innerText += content.at(index)
      index++
      scrollToBottom()
      if (index === content.length) {
        clearInterval(temp)
      }
    }, typeIntervalTime)
}

// 自动滚动到容器底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    requestAnimationFrame(() => {
      messageListRef.value.scrollTop = messageListRef.value.scrollHeight;
    });
  }
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 1440px;
  height: 888px;
  margin: 0 auto;
  padding: 170px 100px 60px;
  // background-image: url(@/assets/images/screen/content-chat.png);
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  border-radius: 40px;
  background: linear-gradient(180deg, rgba(159, 197, 255, 0.69) 0%, rgba(255, 255, 255, 0.17) 100%);
  box-sizing: border-box;
  border: 6px solid #ffffff;
  backdrop-filter: blur(40px);

  position: relative;

  .message-list {
    height: calc(888px - 220px);
    overflow: hidden auto;
    // 隐藏滚动条
    scrollbar-width: none;
  }

  .message {
    margin: 20px 0;
    overflow: hidden;
  }

  .user-message {
    text-align: right;
  }

  .model-message {
    text-align: left;
  }

  .message-content {
    padding: 0 50px;
    font-family: DingTalk JinBuTi;
    font-size: 40px;
    line-height: 70px;
  }

  .user-message .message-content {
    margin-left: auto;
    color: #fffffff2;
  }

  .model-message .message-content {
    margin-right: auto;
    color: #1969ff;
  }
}

.record-btn-container button,
.finish-btn {
  font-family: DingTalk JinBuTi;
  padding: 20px 50px;
  color: #1969ff;
  border-radius: 20px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
  box-sizing: border-box;
  border: 6px solid #ffffff;
  backdrop-filter: blur(153px);
}

// 录音按钮容器样式（原行内样式移入此处）
.record-btn-container {
  position: absolute;
  top: 40px;
  right: 30px;
  text-align: center;
  line-height: 0;
  font-size: 40px;
}

.finish-btn {
  position: absolute;
  top: -200px;
  right: 0px;
  font-size: 80px;
}
</style>
