package com.ruoyi.exams.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户答案对象 user_answers
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
public class UserAnswers extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户考试记录表id */
    @Excel(name = "用户考试记录表id")
    private Long userExamId;

    /** 题目表id */
    @Excel(name = "题目表id")
    private Long questionId;

    /** 选项表id */
    @Excel(name = "选项表id")
    private Long optionId;

    /** 填空题答案 */
    @Excel(name = "填空题答案")
    private String answerText;

    /** 视频地址名称 */
    @Excel(name = "视频地址名称")
    private String urlName;

    /** 视频存储地址 */
    @Excel(name = "视频存储地址")
    private String videoUrl;

    /** 视频时长（秒） */
    @Excel(name = "视频时长", readConverterExp = "秒=")
    private Integer videoDuration;

    /** 视频开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "视频开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 视频结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "视频结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

}
