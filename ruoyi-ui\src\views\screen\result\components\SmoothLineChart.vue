<template>
  <div ref="chartRef" :style="`width: 100%; height: ${proxy.getConvertedValue(600)}px`"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
let chartInstance = null
const { proxy } = getCurrentInstance()

// 模拟数据，xData 改成 1 - 20
const xData = Array.from({ length: 20 }, (_, i) => i + 1)
const yData = ['恐惧', '厌恶', '悲伤', '压抑', '中性', '惊讶', '开心']
// 这里将数据改为与yData对应的类目名称
// const lineData = ['中性', '惊讶', '中性', '压抑', '中性', '惊讶', '中性', '恐惧']

// 随机生成 lineData 数据
const lineData = []
for (let i = 0; i < 20; i++) {
  const randomIndex = Math.floor(Math.random() * yData.length)
  lineData.push(yData[randomIndex])
}

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    // backgroundColor: '#f0f8ff', // 浅蓝色背景
    grid: { left: '10%', right: '5%', top: '0%', bottom: '15%' },
    // tooltip: {
    //   trigger: 'axis',
    //   axisPointer: { type: 'shadow', lineStyle: { color: '#ddd' } }
    // },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData,
      axisLine: { lineStyle: { color: '#ddd' } },
      axisTick: { show: false },
      axisLabel: { color: '#666', fontSize: proxy.getConvertedValue(40) }
    },
    yAxis: {
      type: 'category',
      data: yData.reverse(), // 反转顺序匹配图片
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#666', fontSize: proxy.getConvertedValue(40) },
      splitLine: {
        show: true,
        lineStyle: { type: 'dotted', color: '#ddd' }
      }
    },
    series: [
      {
        name: '',
        type: 'line',
        data: lineData,
        smooth: true,
        // symbol: 'none',
        itemStyle: {
          color: '#4a90e2',
          lineStyle: { color: '#4a90e2', width: 2 }
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0.1, color: 'rgba(0, 94, 255,0.1)' },
            { offset: 0.4, color: 'rgba(0, 94, 255,0.5)' },
            { offset: 1, color: 'rgba(0, 94, 255,0.9)' }
          ])
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', () => chartInstance?.resize())
})

onBeforeUnmount(() => {
  chartInstance?.dispose()
  chartInstance = null
})
</script>
