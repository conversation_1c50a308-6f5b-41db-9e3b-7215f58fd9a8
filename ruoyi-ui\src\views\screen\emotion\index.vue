<template>
  <div class="screen-container">
    <!-- 介绍语句 -->
    <div class="introduction">情绪诱发测试将通过播放视频和图片来测试您的情绪反应，请做好准备</div>
    <!-- 按钮 -->
    <div class="button-container flex justify-center">
      <div class="screen-button text-[#FFFFFF]" @click="handleStart('1')">人脸情绪感知测评</div>
      <div class="screen-button text-[#FFFFFF]" @click="handleStart('2')">情绪辨别测试</div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'

const router = useRouter()

// #region 视频播放
const emit = defineEmits(['handlePlay'])
onMounted(() => {
  emit('handlePlay', 'standby')
})
// #endregion

// 开始测试
const handleStart = id => {
  setTimeout(() => {
    router.push({ name: 'ScreenElicitationTester01', query: { id } })
  }, 300)
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  .introduction {
    padding: 0 148px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 120px;
    text-align: center;
    color: #033682;
  }
  // 按钮
  .button-container {
    margin-top: 124px;
    gap: 81px;
    .screen-button {
      gap: 32px;
      width: 620px;
      height: 680px;
      background-size: 100% 100%;
      border-radius: 72px;
      &:nth-child(1) {
        background-image: url(@/assets/images/screen/bg-test1.png);
      }
      &:nth-child(2) {
        background-image: url(@/assets/images/screen/bg-test2.png);
      }
      padding: 93px 80px;

      font-family: DingTalk JinBuTi;
      font-size: 80px;
      line-height: 260px;
      text-align: center;
    }
  }
}
</style>
