package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.UserAppraisalReport;
import com.ruoyi.exams.service.IUserAppraisalReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户测评报告Controller
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/report")
public class UserAppraisalReportController extends BaseController {

    @Autowired
    private IUserAppraisalReportService userAppraisalReportService;

    /**
     * 查询用户测评报告列表
     */
    @PreAuthorize("@ss.hasPermi('system:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserAppraisalReport userAppraisalReport)
    {
        startPage();
        List<UserAppraisalReport> list = userAppraisalReportService.selectUserAppraisalReportList(userAppraisalReport);
        return getDataTable(list);
    }

    /**
     * 导出用户测评报告列表
     */
    @PreAuthorize("@ss.hasPermi('system:report:export')")
    @Log(title = "用户测评报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserAppraisalReport userAppraisalReport)
    {
        List<UserAppraisalReport> list = userAppraisalReportService.selectUserAppraisalReportList(userAppraisalReport);
        ExcelUtil<UserAppraisalReport> util = new ExcelUtil<UserAppraisalReport>(UserAppraisalReport.class);
        util.exportExcel(response, list, "用户测评报告数据");
    }

    /**
     * 获取用户测评报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:report:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userAppraisalReportService.selectUserAppraisalReportById(id));
    }

    /**
     * 新增用户测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:report:add')")
    @Log(title = "用户测评报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserAppraisalReport userAppraisalReport)
    {
        return toAjax(userAppraisalReportService.insertUserAppraisalReport(userAppraisalReport));
    }

    @Anonymous
    @PostMapping(value = "batchAdd")
    public AjaxResult batchAdd(@RequestBody List<UserAppraisalReport> list) {
        return toAjax(userAppraisalReportService.batchAdd(list));
    }

    /**
     * 修改用户测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:report:edit')")
    @Log(title = "用户测评报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserAppraisalReport userAppraisalReport)
    {
        return toAjax(userAppraisalReportService.updateUserAppraisalReport(userAppraisalReport));
    }

    /**
     * 删除用户测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:report:remove')")
    @Log(title = "用户测评报告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userAppraisalReportService.deleteUserAppraisalReportByIds(ids));
    }

    @GetMapping(value = "getReportInfo")
    public AjaxResult getReportInfo(UserAppraisalReport userAppraisalReport) {
        return AjaxResult.success(userAppraisalReportService.getReportInfo(userAppraisalReport));
    }

    @Scheduled(cron = "0 0 23 * * ?")
    public void processvideo() {
        logger.info("视频处理定时任务启动");
        RestTemplate restTemplate = new RestTemplate();
        String url = "http://117.73.2.9:8000/processvideo/";
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        System.out.println(response);
    }

    public static void main(String[] args) {
        RestTemplate restTemplate = new RestTemplate();
        String url = "http://117.73.2.9:8000/processvideo/";
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        System.out.println(response);
    }

}
