package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.UserExams;
import com.ruoyi.exams.service.IUserExamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 用户考试记录Controller
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RestController
@RequestMapping("/userExams")
public class UserExamsController extends BaseController {

    @Autowired
    private IUserExamsService userExamsService;

    /**
     * 查询用户考试记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:exams:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserExams userExams)
    {
        startPage();
        List<UserExams> list = userExamsService.selectUserExamsList(userExams);
        return getDataTable(list);
    }

    /**
     * 导出用户考试记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:exams:export')")
    @Log(title = "用户考试记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserExams userExams)
    {
        List<UserExams> list = userExamsService.selectUserExamsList(userExams);
        ExcelUtil<UserExams> util = new ExcelUtil<UserExams>(UserExams.class);
        util.exportExcel(response, list, "用户考试记录数据");
    }

    /**
     * 获取用户考试记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:exams:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userExamsService.selectUserExamsById(id));
    }

    /**
     * 新增用户考试记录
     */
    @PreAuthorize("@ss.hasPermi('system:exams:add')")
    @Log(title = "用户考试记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserExams userExams)
    {
        return AjaxResult.success(userExamsService.insertUserExams(userExams));
    }

    /**
     * 修改用户考试记录
     */
    @PreAuthorize("@ss.hasPermi('system:exams:edit')")
    @Log(title = "用户考试记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserExams userExams)
    {
        return toAjax(userExamsService.updateUserExams(userExams));
    }

    /**
     * 删除用户考试记录
     */
    @PreAuthorize("@ss.hasPermi('system:exams:remove')")
    @Log(title = "用户考试记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userExamsService.deleteUserExamsByIds(ids));
    }

    @GetMapping("/queryEvaluationRecord")
    public AjaxResult queryEvaluationRecord(UserExams userExams) {
        Map map = userExamsService.queryEvaluationRecord(userExams);
        return AjaxResult.success(map);
    }

    @GetMapping("/historyList")
    public TableDataInfo historyList(UserExams userExams) {
        startPage();
        List<UserExams> list = userExamsService.historyList(userExams);
        return getDataTable(list);
    }

    @GetMapping("/percentageList")
    public AjaxResult percentageList(UserExams userExams) {
        Map<String, Object> map = userExamsService.percentageList(userExams);
        return AjaxResult.success(map);
    }

}
