package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.Options;
import com.ruoyi.exams.service.IOptionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 选项Controller
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RestController
@RequestMapping("/options")
public class OptionsController extends BaseController {

    @Autowired
    private IOptionsService optionsService;

    /**
     * 查询选项列表
     */
    @PreAuthorize("@ss.hasPermi('system:options:list')")
    @GetMapping("/list")
    public TableDataInfo list(Options options)
    {
        startPage();
        List<Options> list = optionsService.selectOptionsList(options);
        return getDataTable(list);
    }

    /**
     * 导出选项列表
     */
    @PreAuthorize("@ss.hasPermi('system:options:export')")
    @Log(title = "选项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Options options)
    {
        List<Options> list = optionsService.selectOptionsList(options);
        ExcelUtil<Options> util = new ExcelUtil<Options>(Options.class);
        util.exportExcel(response, list, "选项数据");
    }

    /**
     * 获取选项详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:options:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(optionsService.selectOptionsById(id));
    }

    /**
     * 新增选项
     */
    @PreAuthorize("@ss.hasPermi('system:options:add')")
    @Log(title = "选项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Options options)
    {
        return toAjax(optionsService.insertOptions(options));
    }

    /**
     * 修改选项
     */
    @PreAuthorize("@ss.hasPermi('system:options:edit')")
    @Log(title = "选项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Options options)
    {
        return toAjax(optionsService.updateOptions(options));
    }

    /**
     * 删除选项
     */
    @PreAuthorize("@ss.hasPermi('system:options:remove')")
    @Log(title = "选项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(optionsService.deleteOptionsByIds(ids));
    }

    @GetMapping("/batchInsertion")
    public AjaxResult batchInsertion() {
        optionsService.batchInsertion2();
        return AjaxResult.success();
    }

    @GetMapping("/queryTestQuestion")
    public AjaxResult queryTestQuestion(Long id) {
        Map map = optionsService.queryTestQuestion(id);
        return AjaxResult.success(map);
    }

}
