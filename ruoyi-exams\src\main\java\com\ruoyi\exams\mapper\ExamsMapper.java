package com.ruoyi.exams.mapper;

import com.ruoyi.exams.domain.Exams;

import java.util.List;

/**
 * 试卷Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface ExamsMapper {

    /**
     * 查询试卷
     *
     * @param id 试卷主键
     * @return 试卷
     */
    public Exams selectExamsById(Long id);

    /**
     * 查询试卷列表
     *
     * @param exams 试卷
     * @return 试卷集合
     */
    public List<Exams> selectExamsList(Exams exams);

    /**
     * 新增试卷
     *
     * @param exams 试卷
     * @return 结果
     */
    public int insertExams(Exams exams);

    /**
     * 修改试卷
     *
     * @param exams 试卷
     * @return 结果
     */
    public int updateExams(Exams exams);

    /**
     * 删除试卷
     *
     * @param id 试卷主键
     * @return 结果
     */
    public int deleteExamsById(Long id);

    /**
     * 批量删除试卷
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamsByIds(Long[] ids);

}
