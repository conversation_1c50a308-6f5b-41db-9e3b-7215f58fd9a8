package com.ruoyi.exams.mapper;

import com.ruoyi.exams.domain.UserExams;

import java.util.List;
import java.util.Map;

/**
 * 用户考试记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface UserExamsMapper {

    /**
     * 查询用户考试记录
     *
     * @param id 用户考试记录主键
     * @return 用户考试记录
     */
    public UserExams selectUserExamsById(Long id);

    /**
     * 查询用户考试记录列表
     *
     * @param userExams 用户考试记录
     * @return 用户考试记录集合
     */
    public List<UserExams> selectUserExamsList(UserExams userExams);

    /**
     * 新增用户考试记录
     *
     * @param userExams 用户考试记录
     * @return 结果
     */
    public int insertUserExams(UserExams userExams);

    /**
     * 修改用户考试记录
     *
     * @param userExams 用户考试记录
     * @return 结果
     */
    public int updateUserExams(UserExams userExams);

    /**
     * 删除用户考试记录
     *
     * @param id 用户考试记录主键
     * @return 结果
     */
    public int deleteUserExamsById(Long id);

    /**
     * 批量删除用户考试记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserExamsByIds(Long[] ids);

    public List<Map<String, Object>> queryEvaluationRecord(UserExams userExams);

    public List<UserExams> historyList(UserExams userExams);

    public List<Map<String, Object>> percentageList(UserExams userExams);

}
