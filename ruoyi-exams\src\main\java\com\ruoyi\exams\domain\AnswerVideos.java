package com.ruoyi.exams.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 答题视频记录对象 answer_videos
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
public class AnswerVideos extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联考试记录 */
    @Excel(name = "关联考试记录")
    private Long userExamId;

    /** 关联具体答案记录 */
    @Excel(name = "关联具体答案记录")
    private Long userAnswerId;

    /** 关联题目id */
    @Excel(name = "关联题目id")
    private Long questionId;

    /** 视频存储地址 */
    @Excel(name = "视频存储地址")
    private String videoUrl;

    /** 视频时长（秒） */
    @Excel(name = "视频时长", readConverterExp = "秒=")
    private Integer videoDuration;

    /** 视频开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "视频开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 视频结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "视频结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

}
