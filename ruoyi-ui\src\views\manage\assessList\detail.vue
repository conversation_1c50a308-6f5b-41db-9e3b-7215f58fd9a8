<template>
    <div class="app-container">
        <div class="text-center text-[30px] font-bold mb-[40px]">答题详情</div>
        <div class="mx-auto w-[61%]">
            <div class="mb-[40px]" v-for="topicValue, index in topicList" :key="index">
                <div class="mb-[20px] text-[22px]">{{ index > 20 ? index - 21 : index }}、{{ topicValue[0].questionText }}</div>
                <div>
                    <el-radio-group v-model="radioSelect">
                        <div :class="{ 'mr-[1rem]': index != 3 }" v-for="item, index in topicValue" :key="index">
                            <el-radio :disabled="!item.isSelected == radioSelect"
                                :label="item.isSelected">{{ item.optionText }}</el-radio>
                        </div>
                    </el-radio-group>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { listTestQuestion } from '@/api/screen/test'
import { getTopicDetail } from '@/api/test/examsRecord'
import { onMounted } from 'vue'

const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const topicList = ref([])
const radioSelect = ref(1)

/** 查询试卷列表 */
const getList = () => {
    // listTestQuestion({ id: route.query.id }).then(response => {

    // })
    getTopicDetail({ id: route.query.examId }).then(response => {
        topicList.value = response.data
    })
}
onMounted(() => {
    getList()
})
</script>