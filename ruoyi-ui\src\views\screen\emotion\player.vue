<template>
  <div class="screen-container backdrop-blur">
    <carousel-wrapper v-if="materialList.length" v-model="currentIndex" :carousel-list="materialList">
      <div v-for="(item, index) in materialList" :key="index" class="carousel-item">
        <!-- 题目 -->
        <div class="topic flex">
          <p class="count shrink-0 m-0">{{ index + 1 }}</p>
          <p class="text m-0">{{ item.name }}</p>
        </div>

        <!-- 媒体展示区域 -->
        <div class="media-container">
          <!-- 视频播放区域 -->
          <div v-if="item.type === 'video'" class="video-wrapper">
            <video
              :ref="
                el => {
                  if (el) videoRefs[index] = el
                }
              "
              class="video-player"
              :src="item.url"
              @ended="handleVideoEnded(item, index)"
              @error="handleVideoError(item, index)"
              @play="handleVideoPlay(item, index)"
              @pause="handleVideoPause(item, index)"
              preload="auto"
              controlsList="nodownload"
            ></video>

            <!-- 视频控制按钮 -->
            <div class="video-controls" @click="toggleVideoPlay(item, index)">
              <el-icon v-if="isVideoEnded[index]" class="control-icon"><RefreshRight /></el-icon>
              <el-icon v-else-if="!isPlaying[index]" class="control-icon"><VideoPlay /></el-icon>
            </div>
          </div>

          <!-- 图片展示区域 -->
          <div v-else-if="item.type === 'image'" class="image-wrapper">
            <img :src="item.url" alt="情绪诱发图片" class="image-display" />
          </div>
        </div>

        <!-- 评价按钮 -->
        <div class="action-buttons">
          <div class="evaluate-button" :class="{ evaluating: isEvaluating[index], evaluated: hasEvaluated[index] }" @click="toggleEvaluation(item, index)">
            {{ isEvaluating[index] ? '结束评价' : hasEvaluated[index] ? '已评价' : '开始评价' }}
          </div>

          <!-- <div v-if="canFinishTest" class="finish-button" @click="handleFinishTest">完成测试</div> -->
        </div>
      </div>
    </carousel-wrapper>
    <div v-if="canFinishTest" class="finish-button" @click="handleFinishTest">完成测试</div>

    <!-- 加载动画 -->
    <div v-if="showLoading" class="loading-overlay">
      <div class="loading-modal">
        <div class="loading-spinner"></div>
        <h2>{{ loadingText }}</h2>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import CarouselWrapper from '@/views/screen/components/CarouselWrapper'
import { VideoPlay, RefreshRight } from '@element-plus/icons-vue'
import microExpression from '@/assets/video/demo/micro-expression.m4v'
import bgTest1 from '@/assets/images/screen/bg-test1.png'
/*
import ep01 from '@/assets/emotion/pictures/01.png'
import ep02 from '@/assets/emotion/pictures/02.png'
import ep03 from '@/assets/emotion/pictures/03.png'
import ep04 from '@/assets/emotion/pictures/04.png'
*/
import bgTest2 from '@/assets/images/screen/bg-test2.png'

const router = useRouter()
const currentIndex = ref(0)
const showLoading = ref(false)
const loadingText = ref('请稍候...')
const videoRefs = ref({})

// 播放状态
const isPlaying = ref({})
const isVideoEnded = ref({})

// 评价状态
const isEvaluating = ref({})
const hasEvaluated = ref({})
const evaluationResults = ref({})

// 示例素材列表 - 使用已有的资源
const materialList = ref([
  { id: 1, name: '快乐情绪视频', type: 'video', url: microExpression, emotion: 'happy' },
  { id: 2, name: '悲伤情绪视频', type: 'video', url: microExpression, emotion: 'sad' },
  /*{ id: 3, name: '请观察图片后给出评价', type: 'image', url: ep01, emotion: 'angry' },
  { id: 4, name: '请观察图片后给出评价', type: 'image', url: ep02, emotion: 'fear' },
  { id: 5, name: '请观察图片后给出评价', type: 'image', url: ep03, emotion: 'fear' },
  { id: 6, name: '请观察图片后给出评价', type: 'image', url: ep04, emotion: 'fear' }*/
])

// 是否可以完成测试（至少评价了一个素材）
const canFinishTest = computed(() => {
  return Object.values(hasEvaluated.value).some(value => value === true)
})

// 定义事件
const emit = defineEmits(['startRecording', 'stopRecording', 'handlePlay'])

// 切换视频播放状态
const toggleVideoPlay = (_item, index) => {
  // 使用 videoRefs 获取视频元素
  const video = videoRefs.value[index]

  if (!video) {
    console.error('找不到视频元素:', index)
    return
  }

  console.log('尝试播放/暂停视频:', index, video)

  try {
    if (isVideoEnded.value[index]) {
      // 如果视频已结束，重新播放
      video.currentTime = 0
      isVideoEnded.value[index] = false
    }

    if (isPlaying.value[index]) {
      // 暂停视频
      video.pause()
      console.log('视频已暂停')
    } else {
      // 播放视频 - 使用 Promise 处理
      const playPromise = video.play()

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('视频开始播放')
            isPlaying.value[index] = true
          })
          .catch(error => {
            console.error('视频播放失败:', error)
            // 尝试静音播放（解决自动播放策略问题）
            video.muted = true
            video.play().catch(err => {
              console.error('静音播放也失败:', err)
            })
          })
      }
    }
  } catch (error) {
    console.error('视频操作错误:', error)
  }
}

// 视频播放事件处理
const handleVideoPlay = (_item, index) => {
  isPlaying.value[index] = true
}

// 视频暂停事件处理
const handleVideoPause = (_item, index) => {
  isPlaying.value[index] = false
}

// 视频结束事件处理
const handleVideoEnded = (_item, index) => {
  console.log(`视频 ${index} 播放结束`)
  isPlaying.value[index] = false
  isVideoEnded.value[index] = true

  // 强制更新状态
  setTimeout(() => {
    isVideoEnded.value = { ...isVideoEnded.value }
  }, 0)
}

// 视频错误事件处理
const handleVideoError = (item, index) => {
  console.error('视频播放错误:', item)
  isPlaying.value[index] = false
}

// 监听轮播索引变化
watch(currentIndex, (newIndex, oldIndex) => {
  console.log(`轮播从 ${oldIndex} 切换到 ${newIndex}`)

  // 停止所有视频播放
  Object.keys(videoRefs.value).forEach(key => {
    const video = videoRefs.value[key]
    if (video) {
      video.pause()
      isPlaying.value[key] = false
    }
  })
})

// 切换评价状态
const toggleEvaluation = (item, index) => {
  if (isEvaluating.value[index]) {
    // 结束评价
    stopEvaluation(item, index)
  } else {
    // 开始评价
    startEvaluation(item, index)
  }
}

// 开始评价
const startEvaluation = (item, index) => {
  isEvaluating.value[index] = true

  // 调用父组件的开始录制方法
  emit('startRecording')

  console.log(`开始评价素材: ${item.name}`)
}

// 结束评价
const stopEvaluation = (item, index) => {
  isEvaluating.value[index] = false
  hasEvaluated.value[index] = true

  // 调用父组件的停止录制方法
  emit('stopRecording', {
    id: `emotion_${item.id}`,
    materialId: item.id,
    callback: function (res) {
      // 保存评价结果
      evaluationResults.value[item.id] = {
        materialId: item.id,
        materialName: item.name,
        materialType: item.type,
        materialEmotion: item.emotion,
        videoUrl: res.url,
        timestamp: new Date().toISOString()
      }

      console.log(`素材 ${item.name} 的评价已保存，视频URL: ${res.url}`)
    }
  })
}

// 完成测试
const handleFinishTest = () => {
  showLoading.value = true
  loadingText.value = '正在处理评价结果...'

  // 模拟提交评价结果
  setTimeout(() => {
    console.log('评价结果:', evaluationResults.value)
    showLoading.value = false

    // 跳转回主页
    router.push({ name: 'ScreenPreIndex' })
  }, 2000)
}

// 组件挂载
onMounted(() => {
  // 触发父组件的数字人视频播放
  emit('handlePlay', 'standby')

  // 初始化状态
  materialList.value.forEach((_item, index) => {
    isPlaying.value[index] = false
    isVideoEnded.value[index] = false
    isEvaluating.value[index] = false
    hasEvaluated.value[index] = false
  })

  // 确保视频元素加载完成后设置初始状态
  setTimeout(() => {
    // 强制更新状态对象，确保响应式
    isPlaying.value = { ...isPlaying.value }
    isVideoEnded.value = { ...isVideoEnded.value }
  }, 500)
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 如果正在评价，停止评价
  materialList.value.forEach((item, index) => {
    if (isEvaluating.value[index]) {
      stopEvaluation(item, index)
    }
  })
})
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;

  .carousel-item {
    padding: 70px 140px;
    flex: 0 0 100%;
    min-width: 100%;
    box-sizing: border-box;
    backface-visibility: hidden;
  }

  /* 题目样式 */
  .topic {
    column-gap: 41px;
    .count {
      width: 96px;
      height: 120px;
      background-image: url(@/assets/images/screen/bg-count.png);
      background-size: 100% 100%;
      padding-top: 36px;
      text-align: center;
      font-family: Archivo Black;
      font-size: 56px;
      font-weight: normal;
      line-height: 56px;
      color: #0d478e;
    }
    .text {
      margin-top: 14px;
      font-family: AlibabaPuHuiTiBold;
      font-size: 72px;
      line-height: 106px;
    }
  }

  // #region 按钮提出来
  .finish-button {
    background-color: #005eff;
    color: #ffffff;
    border: 4px solid #005eff;
    position: absolute;
    top: -160px;
    right: 60px;
  }

  .evaluate-button,
  .finish-button {
    padding: 20px 40px;
    border-radius: 20px;
    font-family: AlibabaPuHuiTiBold;
    font-size: 48px;
    cursor: pointer;
    transition: all 0.2s;

    &:active {
      transform: scale(0.96);
    }
  }
  // #endregion
  /* 媒体容器样式 */
  .media-container {
    margin-top: 80px;
    display: flex;
    justify-content: center;

    .video-wrapper,
    .image-wrapper {
      position: relative;
      width: 100%;
      height: 600px;
      background-color: #000;
      border-radius: 32px;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;

      .video-player,
      .image-display {
        max-width: 100%;
        max-height: 100%;
      }

      .video-controls {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.3);

        .control-icon {
          font-size: 120px;
          color: white;
          filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.5));
        }
      }
    }
  }

  /* 操作按钮样式 */
  .action-buttons {
    margin-top: 60px;
    display: flex;
    justify-content: flex-end;
    gap: 40px;

    .evaluate-button {
      background-color: #ffffff;
      color: #005eff;
      border: 4px solid #005eff;

      &.evaluating {
        background-color: #ff5252;
        color: #ffffff;
        border-color: #ff5252;
        animation: pulse 1.5s infinite;
      }

      &.evaluated {
        background-color: #4caf50;
        color: #ffffff;
        border-color: #4caf50;
      }
    }
  }
}

/* 加载动画样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-modal {
  background-color: white;
  border-radius: 32px;
  padding: 60px;
  width: 80%;
  max-width: 1200px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

  h2 {
    font-family: AlibabaPuHuiTiBold;
    font-size: 72px;
    color: #005eff;
    margin-top: 40px;
  }

  .loading-spinner {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border: 16px solid #f3f3f3;
    border-top: 16px solid #005eff;
    border-radius: 50%;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
</style>
