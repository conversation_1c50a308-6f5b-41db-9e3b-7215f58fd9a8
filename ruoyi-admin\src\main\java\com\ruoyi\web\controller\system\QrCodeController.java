package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.framework.web.service.SysLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/qrCode")
public class QrCodeController {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysLoginService loginService;

    @GetMapping("/uuid")
    @Anonymous
    public AjaxResult generateUuid() {
        String uuid = UUID.randomUUID().toString();
        // 存储到Redis，有效期5分钟
        redisCache.setCacheObject("qrCode:" + uuid, "unused", 5, TimeUnit.MINUTES);
        return AjaxResult.success("success", uuid);
    }

    @PostMapping("/submit")
    @Anonymous
    public AjaxResult submitLogin(@RequestBody LoginBody loginBody) {

        Boolean qrCodeBoolean = redisCache.hasKey("qrCode:" + loginBody.getUuid());

        if (!qrCodeBoolean) {
            return AjaxResult.error("二维码已过期");
        }

//        String cacheObject = redisCache.getCacheObject("qrCode:" + loginBody.getUuid());

        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());

        redisCache.setCacheObject("qrCode:" + loginBody.getUuid(), token, 5, TimeUnit.MINUTES);

        return AjaxResult.success("登录成功");
    }

    @GetMapping("/check")
    @Anonymous
    public AjaxResult checkLogin(@RequestParam String uuid) {

        Boolean qrCodeBoolean = redisCache.hasKey("qrCode:" + uuid);

        if (!qrCodeBoolean) {
            return AjaxResult.error("二维码已过期");
        }

        String cacheObject = redisCache.getCacheObject("qrCode:" + uuid);

        if (cacheObject != null) {
            redisCache.deleteObject("qrcode:" + uuid);
            return AjaxResult.success(cacheObject);
        }
        return null;
    }

}
