package com.ruoyi.exams.mapper;

import com.ruoyi.exams.domain.Options;

import java.util.List;
import java.util.Map;

/**
 * 选项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface OptionsMapper {

    /**
     * 查询选项
     *
     * @param id 选项主键
     * @return 选项
     */
    public Options selectOptionsById(Long id);

    /**
     * 查询选项列表
     *
     * @param options 选项
     * @return 选项集合
     */
    public List<Options> selectOptionsList(Options options);

    /**
     * 新增选项
     *
     * @param options 选项
     * @return 结果
     */
    public int insertOptions(Options options);

    /**
     * 修改选项
     *
     * @param options 选项
     * @return 结果
     */
    public int updateOptions(Options options);

    /**
     * 删除选项
     *
     * @param id 选项主键
     * @return 结果
     */
    public int deleteOptionsById(Long id);

    /**
     * 批量删除选项
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOptionsByIds(Long[] ids);

    public List<Map<String, Object>> queryTestQuestion(Long id);

}
