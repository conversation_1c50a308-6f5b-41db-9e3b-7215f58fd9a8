<template>
  <div class="screen-container backdrop-blur">
    <div class="carousel-wrapper" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd" @touchcancel="handleTouchEnd">
      <div class="carousel-track" :style="trackStyle" :class="{ 'transition-effect': enableTransition }">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  carouselList: { type: Array, default: () => [] },
  modelValue: { required: true, type: Number }
})

// get/set计算属性currentIndex
const currentIndex = computed({
  get: () => props.modelValue,
  set: newIndex => {
    emit('update:modelValue', newIndex)
  }
})

// 监听props.modelValue变化，更新currentTranslateX
watch(
  () => props.modelValue,
  () => {
    currentTranslateX.value = -currentIndex.value * containerWidth.value
  }
)

const emit = defineEmits()
const touchStartX = ref(0)
const currentTranslateX = ref(0)
const enableTransition = ref(true)
const isDragging = ref(false)
const containerWidth = ref(0)

// 精准获取容器宽度（修复计算误差）
const getContainerWidth = () => {
  const container = document.querySelector('.carousel-wrapper')
  return container ? Math.round(container.getBoundingClientRect().width) : 0
}

// 实时轨道位移计算
const trackStyle = computed(() => ({
  transform: `translateX(${currentTranslateX.value}px)`
}))

// 触摸事件处理（修复方向判断）
const handleTouchStart = e => {
  enableTransition.value = false
  isDragging.value = true
  touchStartX.value = e.touches[0].clientX
}

const handleTouchMove = e => {
  if (!isDragging.value) return
  const currentX = e.touches[0].clientX
  const diffX = currentX - touchStartX.value
  currentTranslateX.value = -currentIndex.value * containerWidth.value + diffX
}

const handleTouchEnd = () => {
  if (!isDragging.value) return
  isDragging.value = false
  enableTransition.value = true

  const diffX = currentTranslateX.value + currentIndex.value * containerWidth.value
  const absDiff = Math.abs(diffX)
  const threshold = containerWidth.value * 0.15 // 优化滑动灵敏度

  // 修复方向判断逻辑
  if (absDiff > threshold) {
    currentIndex.value =
      diffX > 0
        ? Math.max(currentIndex.value - 1, 0) // 向右滑动
        : Math.min(currentIndex.value + 1, props.carouselList.length - 1) // 向左滑动
  }

  // 强制精准定位（修复像素偏差）
  // 与父组件经过了 currentIndex 计算属性双向绑定，使用 nextTick 确保更新生效，与上面的watch监听重复，但这里是确保第一个右滑，最后一个左滑的过渡效果正常
  nextTick(() => {
    currentTranslateX.value = -currentIndex.value * containerWidth.value
  })
}

// 容器尺寸响应式处理
const updateDimensions = () => {
  containerWidth.value = getContainerWidth()
  currentTranslateX.value = -currentIndex.value * containerWidth.value
}

onMounted(() => {
  updateDimensions()
  window.addEventListener('resize', updateDimensions)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateDimensions)
})
</script>

<style lang="scss" scoped>
/* 轮播样式 */
.carousel-wrapper {
  width: 100%;
  overflow: hidden;
  touch-action: pan-y;

  .carousel-track {
    display: flex;
    height: 100%;
    will-change: transform;

    &.transition-effect {
      transition: transform 0.35s cubic-bezier(0.18, 0.89, 0.32, 1.28); // 优化动画曲线
    }
    // .carousel-item {
    //   padding: 70px 140px;
    //   flex: 0 0 100%;
    //   min-width: 100%;
    //   box-sizing: border-box;
    //   backface-visibility: hidden;
    // }
  }
}
</style>
