<template>
  <div class="screen-container">
    <div class="page-title">心理健康测试</div>
    <section class="result-data flex flex-col">
      <!-- 测评工具介绍 -->
      <!-- <div v-show="false" class="item text-justify">
        SCL--90症状自评表是世界上最著名的心理健康测试量表之一，80年代引入我国，并在精神科和心理健康门诊的临床工作中得到广泛应用。该量表是以Derogatis编制的Hopkin's病状清单
        (HSCL1973)为基础，主要从感觉、情感、思维、意识、行为直到生活习惯、人防关系、饮食睡眠等多种角度，评定一个人是否有某种心理症状及其严重程度如何。与其他的自评量表(如SDS、SAS等)相比，有容量大、反映症状丰富，更能准确刻画受测者的自觉症状特性等优点。SCL-90作为一种适用面广、包含病理心理症状项目多的自评量表，在临床上具有不可替代的作用，是一种十分有效的评定工具。
      </div> -->
      <!-- 测评人信息 -->
      <div class="item flex flex-col">
        <p class="user-info m-0" v-for="(item, index) in userInfo" :key="index">
          <span>{{ `${item.label}：` }}</span>
          <span>{{ item.value }}</span>
        </p>
      </div>
      <!-- 微表情识别 -->
      <!-- <div class="text-[length:50px] text-center"><a href="https://lckczx.oss.cn-north-3.inspurcloudoss.com/MER/result/2025/4/22/125/recording_2.mp4">点击下载视频</a></div> -->
      <div class="item flex justify-center relative">
        <div class="video-container">
          <video @play="play" ref="preview" class="videoBox" @error="nextVideo" @ended="nextVideo" :src="videoList[0]" crossorigin></video>
          <!-- 增加canvas用于人脸关键点绘制 -->
          <canvas ref="faceCanvas" class="face-canvas"></canvas>
        </div>
      </div>

      <div>
        <div class="text-center mb-[20px] text-[0.5rem]">结论</div>
        <div v-if="route.query.examId == 1" class="text-[0.4rem] leading-[0.7rem]">
          <div v-if="scoreValue > 20">
            您的总分为{{
              scoreValue
            }}，建议您寻求专业的支持和指导，以便更好地理解和照顾自己的心理需求。请记住，无论评估结果如何，都有许多方法可以帮助您维护一个健康的心理状态，重要的是要对自己保持关爱和支持。
          </div>
          <div v-else-if="scoreValue > 15">
            您的总分为{{ scoreValue }}，您可能需要更多关注自身的心理状况，考虑采取一些措施进行调整和改善，比如增加休闲活动、与亲朋好友交流等。
          </div>
          <div v-else>您的总分为{{ scoreValue }}，这表明在当前的评估中您的心理状态良好，继续保持积极的心态是非常不错的方向。</div>
        </div>
        <div v-if="route.query.examId == 2" class="text-[0.4rem] leading-[0.7rem]">
          <div v-if="scoreValue > 70">
            您的总分为{{
              scoreValue
            }}，建议您考虑寻求专业的指导和支持，以便更好地理解当前的感受并找到最适合自己的应对策略。记住，关注自己的心理健康是一件非常重要且值得的事情，您不必独自面对这些挑战。无论评估的结果如何，都有许多资源和方法可以帮助您维护一个健康、平衡的生活状态
          </div>
          <div v-else-if="scoreValue > 59">
            您的总分为{{
              scoreValue
            }}，这可能是身体和心灵在告诉您需要稍微放缓脚步，给自己多一点关爱。探索不同的方法来缓解压力，比如适度的运动、冥想或是与信任的人分享您的感受，都可能对您有所帮助。
          </div>
          <div v-else-if="scoreValue > 49">
            您的总分为{{
              scoreValue
            }}，这可能提示您在生活中遇到了一些小挑战或压力点。这种情况是非常常见的，也许通过引入一些放松技巧或者增加自我关怀的时间，可以有效地帮助您感到更加轻松和平静。
          </div>
          <div v-else>
            您的总分为{{ scoreValue }}，这表明您目前的心理状态相当稳定，面对日常生活的挑战时保持了良好的心态。继续保持积极的生活方式和心态会对您的整体健康非常有益。
          </div>
        </div>
      </div>

      <!-- 心理情绪占比 -->
      <!-- <div class="item">
        <pie-chart ref="pieChartRef" />
      </div> -->
      <!-- 微表情情绪分值 -->
      <!-- <div class="item">
        <micro-expression-table ref="microRef" />
      </div> -->
      <!-- 情绪分布 -->
      <!-- <div class="item">
        <smooth-line-chart />
      </div> -->
    </section>
    <!-- 按钮 -->
    <div class="button-container flex">
      <div class="screen-button bg-[#005EFF] text-[#FFFFFF]" @click="router.push({ name: 'ScreenTestIndex' })">重新测评</div>
      <div class="screen-button bg-[#EAF3FF] text-[#005EFF]" @click="router.push({ name: 'ScreenHistory' })">返 回</div>
    </div>
  </div>
</template>

<script setup>
import useUserStore from '@/store/modules/user'
import PieChart from './components/3DPieChart' // 3D饼图
import MicroExpressionTable from './components/MicroExpressionTable' // 微表情表
import SmoothLineChart from './components/SmoothLineChart' // 平滑线图
import { getPercentageList, getReportInfo } from '@/api/test/examsRecord'
import microExpression from '@/assets/video/demo/micro-expression.m4v'
import { onMounted, onBeforeUnmount, ref } from 'vue'
// 引入人脸检测模块
import { useFaceDetection } from '@/layout/screen/components/VideoRecord/useFaceDetection'
import { useRouter, useRoute } from 'vue-router'
import { getCurrentInstance } from 'vue'

const router = useRouter(),
  route = useRoute(),
  { proxy } = getCurrentInstance(),
  userStore = useUserStore(),
  userInfo = ref([
    { label: '姓名', value: userStore.user.nickName },
    { label: '性别', value: userStore.user.sex === '0' ? '男' : '女' },
    { label: '登录名', value: userStore.user.userName },
    { label: '测试时间', value: '2025' },
    { label: '测试用时', value: '2025' }
  ])
const examType = ref({})
const videoList = ref([])
const currentIndex = ref(0) // 当前播放视频的索引
const pieChartRef = ref(null)
const microRef = ref(null)
const scoreValue = ref(0)
//视频连续播放
const nextVideo = async () => {
  if (currentIndex.value < videoList.value.length - 1) {
    currentIndex.value++ // 切换到下一个视频
  } else {
    currentIndex.value = 0 // 如果已经是最后一个视频，循环到第一个
  }

  // 暂停当前视频
  if (preview.value) {
    preview.value.pause();
  }

  // 清理之前的人脸检测
  cleanup();

  // 设置新的视频源
  if (preview.value && videoList.value[currentIndex.value]) {
    preview.value.src = videoList.value[currentIndex.value];

    // 重置人脸检测状态
    faceDetecting.value = false;

    try {
      // 加载并播放新视频
      await preview.value.load();
      await preview.value.play();

      // 视频元数据加载完成后会触发play事件，
      // 在play事件处理函数中会检测视频方向并初始化人脸检测
    } catch (error) {
      console.error('视频切换失败:', error);
    }
  }
}
// 生成随机颜色的函数（目前未使用）
// const randomColor = () => {
//   let letters = '0123456789ABCDEF'
//   let color = '#'
//   for (let i = 0; i < 6; i++) {
//     color += letters[Math.floor(Math.random() * 16)]
//   }
//   return color
// }

// route.query.id &&
//   getPercentageList({ examId: route.query.id }).then(res => {
//     microRef.value.tableData = res.data.list
//     pieChartRef.value.data[0].value = Number(res.data.pie.positive)
//     pieChartRef.value.data[1].value = Number(res.data.pie.negative)
//     pieChartRef.value.data[2].value = Number(res.data.pie.others)
//     pieChartRef.value.initChart()
//   })

route.query.id &&
  getReportInfo({ userId: userStore.id, examId: route.query.id, type: 0 }).then(res => {
    let videoUrlLixt = res.data.report.urlList
    scoreValue.value = res.data.report.score
    videoUrlLixt.forEach((element) => {
      if (element) {
        let videoUrl = element.replace('result', 'original')
        videoList.value.push(videoUrl)
      }
    })
    // convertVideo(videoList.value[0])
    const { startTime, endTime } = res.data.user
    userInfo.value[3].value = proxy.parseTime(startTime, '{y}/{m}/{d} {h}:{i}:{s}')
    userInfo.value[4].value = proxy.calculateDuration(startTime, endTime)
  })

onMounted(() => {
  // 视频加载完成后自动播放
  if (preview.value) {
    preview.value.oncanplay = () => {
      preview.value.play().catch(err => {
        console.error('视频播放失败:', err)
      })
    }
  }
})

// 视频和画布引用
const preview = ref(null)
const faceCanvas = ref(null)

// 使用人脸检测模块
const { /* faceDetected, */ faceDetecting, initFaceDetection, cleanup } = useFaceDetection()

// 视频准备好时初始化人脸检测
const handleVideoReady = async () => {
  // 确保已经检测了视频方向
  detectVideoOrientation();

  // 添加窗口大小变化监听，确保在窗口大小变化时重新计算Canvas尺寸
  window.addEventListener('resize', handleResize);

  // 初始化人脸检测
  await initFaceDetection(preview.value, faceCanvas.value);

  // 在初始化完成后再次检查尺寸，确保Canvas与视频完全匹配
  setTimeout(detectVideoOrientation, 500);
}

// 播放视频
const play = () => {
  // 检测视频方向（竖屏还是横屏）
  const videoEl = preview.value;
  if (videoEl) {
    // 等待视频元数据加载完成
    if (videoEl.readyState >= 1) {
      detectVideoOrientation();
    } else {
      // 如果元数据尚未加载，添加事件监听器
      videoEl.addEventListener('loadedmetadata', detectVideoOrientation, { once: true });
    }
  }

  // 视频开始播放时初始化人脸检测，已经开始检测后，不再初始化
  if (!faceDetecting.value) handleVideoReady();
}

// 检测视频方向并设置相应的属性
const detectVideoOrientation = () => {
  const videoEl = preview.value;
  const canvasEl = faceCanvas.value;

  if (!videoEl || !canvasEl) return;

  // 获取视频的实际尺寸
  const videoWidth = videoEl.videoWidth;
  const videoHeight = videoEl.videoHeight;

  // 获取容器尺寸
  const containerEl = videoEl.parentElement;
  const containerWidth = containerEl.clientWidth;
  const containerHeight = containerEl.clientHeight;

  // 判断是否为竖屏视频
  const isPortrait = videoHeight > videoWidth;
  console.log(`结果页视频方向: ${isPortrait ? '竖屏' : '横屏'}, 尺寸: ${videoWidth}x${videoHeight}, 容器尺寸: ${containerWidth}x${containerHeight}`);

  // 设置视频和Canvas的方向属性
  videoEl.dataset.orientation = isPortrait ? 'portrait' : 'landscape';
  canvasEl.dataset.orientation = isPortrait ? 'portrait' : 'landscape';

  // 调整Canvas尺寸
  if (isPortrait) {
    // 对于竖屏视频，我们需要计算视频在容器中的实际显示尺寸
    // 当使用object-fit: cover时，视频会被缩放以覆盖整个容器

    // 计算视频的宽高比
    const videoRatio = videoWidth / videoHeight;
    // 计算容器的宽高比
    const containerRatio = containerWidth / containerHeight;

    if (containerRatio > videoRatio) {
      // 容器更宽，视频会按宽度缩放
      const scaledHeight = containerWidth / videoRatio;
      canvasEl.width = containerWidth;
      canvasEl.height = scaledHeight;
      console.log(`竖屏视频按宽度缩放: ${canvasEl.width}x${canvasEl.height}`);
    } else {
      // 容器更高，视频会按高度缩放
      const scaledWidth = containerHeight * videoRatio;
      canvasEl.width = scaledWidth;
      canvasEl.height = containerHeight;
      console.log(`竖屏视频按高度缩放: ${canvasEl.width}x${canvasEl.height}`);
    }
  } else {
    // 横屏视频，直接使用视频的原始尺寸
    canvasEl.width = videoWidth;
    canvasEl.height = videoHeight;
  }
}

// 窗口大小变化处理函数
const handleResize = () => {
  setTimeout(detectVideoOrientation, 100);
};

// 组件卸载时清理资源
onBeforeUnmount(() => {
  // 清理人脸检测资源
  cleanup();

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize);
})
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  padding: 147px 90px 180px;
  background-image: url('@/assets/images/screen/result/bg-page.png'), url('@/assets/images/screen/result/bg-page2.png'), url('@/assets/images/screen/result/bg-page.png');
  background-size: 100% auto, 100% auto;
  background-repeat: no-repeat;
  background-position: 0 0, 0 2800px, 0 5600px;

  background-size: 100% auto, 100% auto;

  .page-title {
    width: 799.2px;
    height: 100.8px;
    background-image: url('@/assets/images/screen/result/bg-title.png');
    background-size: 100% 100%;
    padding-top: 5px;
    margin: 0 auto 130px;

    font-family: AlibabaPuHuiTiBold;
    font-size: 80px;
    line-height: 80px;
    color: #3d3d3d;
    text-align: center;
  }

  .result-data {
    gap: 120px;

    .item {
      background-size: 100% 100%;

      @for $i from 1 through 5 {
        &:nth-child(#{$i}) {
          background-image: url('@/assets/images/screen/result/bg-item#{$i}.png');
        }
      }

      // // 测评工具介绍
      // &:nth-child(1) {
      //   height: 964px;
      //   padding: 230px 80px 0;

      //   font-family: AlibabaPuHuiTiRegular;
      //   font-size: 40px;
      //   font-weight: normal;
      //   line-height: 72px;
      //   color: #3d3d3d;
      //   letter-spacing: 2px;
      // }

      // 测评人信息
      &:nth-child(1) {
        height: 884px;
        padding: 275px 0 0 280px;
        gap: 68px;

        .user-info {
          line-height: 48px;
          font-size: 48px;

          span {
            &:nth-child(1) {
              font-family: DingTalk JinBuTi;
              color: #033682;
              display: inline-block;
              width: 240px;
              margin-right: 225px;
            }

            &:nth-child(2) {
              font-family: AlibabaPuHuiTiRegular;
              color: #3d3d3d;
            }
          }
        }
      }

      // 微表情识别
      &:nth-child(2) {
        width: 1420px;
        height: 1014px;

        .video-container {
          position: relative;
          width: 1260px;
          height: 690px;
          border-radius: 32px;
          overflow: hidden;
          margin-top: 244px;
        }

        .videoBox {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transform: scale(-1, 1);
        }

        /* 竖屏视频样式 */
        .videoBox[data-orientation="portrait"] {
          width: 100%;
          height: 100%;
          object-fit: cover; /* 使用cover确保铺满容器 */
        }

        .face-canvas {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
          pointer-events: none;
          transform: scale(-1, 1);
        }

        /* 竖屏Canvas样式 */
        .face-canvas[data-orientation="portrait"] {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          /* 确保Canvas完全覆盖视频 */
        }
      }

      // 心理情绪占比
      &:nth-child(3) {
        height: 844px;
        padding: 187px 0 0 0;
      }

      // 微表情情绪分值
      &:nth-child(4) {
        height: 1404px;
        padding: 271px 80px 0;
      }

      // 情绪分布
      &:nth-child(5) {
        height: 923.5px;
        padding: 280px 80px 0;
      }
    }
  }

  // 按钮
  .button-container {
    margin-top: 176px;
    gap: 60px;

    .screen-button {
      width: 680px;
      height: 200px;
      border-radius: 32px;
      font-family: AlibabaPuHuiTiBold;
      font-size: 72px;
      line-height: 200px;
      text-align: center;
      border: 4px solid #005eff;
    }
  }
}
</style>
