package com.ruoyi.exams.service;

import com.ruoyi.exams.domain.UserAppraisalReport;

import java.util.List;
import java.util.Map;

/**
 * 用户测评报告Service接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IUserAppraisalReportService {

    /**
     * 查询用户测评报告
     *
     * @param id 用户测评报告主键
     * @return 用户测评报告
     */
    public UserAppraisalReport selectUserAppraisalReportById(Long id);

    /**
     * 查询用户测评报告列表
     *
     * @param userAppraisalReport 用户测评报告
     * @return 用户测评报告集合
     */
    public List<UserAppraisalReport> selectUserAppraisalReportList(UserAppraisalReport userAppraisalReport);

    /**
     * 新增用户测评报告
     *
     * @param userAppraisalReport 用户测评报告
     * @return 结果
     */
    public int insertUserAppraisalReport(UserAppraisalReport userAppraisalReport);

    public int batchAdd(List<UserAppraisalReport> list);

    /**
     * 修改用户测评报告
     *
     * @param userAppraisalReport 用户测评报告
     * @return 结果
     */
    public int updateUserAppraisalReport(UserAppraisalReport userAppraisalReport);

    /**
     * 批量删除用户测评报告
     *
     * @param ids 需要删除的用户测评报告主键集合
     * @return 结果
     */
    public int deleteUserAppraisalReportByIds(Long[] ids);

    /**
     * 删除用户测评报告信息
     *
     * @param id 用户测评报告主键
     * @return 结果
     */
    public int deleteUserAppraisalReportById(Long id);

    public Map<String, Object> getReportInfo(UserAppraisalReport userAppraisalReport);

}
