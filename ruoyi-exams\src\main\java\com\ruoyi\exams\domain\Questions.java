package com.ruoyi.exams.domain;

import java.math.BigDecimal;

import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 题目对象 questions
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
public class Questions extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 试卷id */
    @Excel(name = "试卷id")
    private Long examId;

    /** 题目 */
    @Excel(name = "题目")
    private String questionText;

    /** 题目类型 */
    @Excel(name = "题目类型")
    private Integer questionType;

    /** 分数 */
    @Excel(name = "分数")
    private BigDecimal score;

    /** 排序 */
    @Excel(name = "排序")
    private Integer scoreOrder;

    /** 反向评分 */
    @Excel(name = "反向评分")
    private Integer reverseScore;

}
