package com.ruoyi.exams.controller;

import com.inspurcloud.oss.client.impl.OSSClientImpl;
import com.inspurcloud.oss.model.CannedAccessControlList;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.uuid.UUID;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import javax.imageio.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;

@RestController
@RequestMapping("/fileUpload")
public class FileUploadController extends BaseController {

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.accessKey}")
    private String accessKey;

    @Value("${oss.secretKey}")
    private String secretKey;

    @Value("${oss.bucketName}")
    private String bucketName;

    @Value("${oss.resourceUrl}")
    private String resourceUrl;

    @Log(title = "浪潮OSS云资源上传", businessType = BusinessType.OTHER)
    @PostMapping(value = "/simpleUpload")
    @CrossOrigin
    public AjaxResult simpleUpload(@RequestParam("file") MultipartFile file, @RequestParam("id") String id) throws IOException {

        // 允许上传的文件类型
        java.util.List<String> allowedFileTypes = new ArrayList<String>();
        allowedFileTypes.add("mp4");
        allowedFileTypes.add("png");
        allowedFileTypes.add("jpeg");
        allowedFileTypes.add("txt");
        allowedFileTypes.add("doc");
        allowedFileTypes.add("docx");
        allowedFileTypes.add("pdf");
        allowedFileTypes.add("odt");
        allowedFileTypes.add("xls");
        allowedFileTypes.add("xlsx");
        allowedFileTypes.add("ppt");
        allowedFileTypes.add("pptx");
        allowedFileTypes.add("jpg");
        allowedFileTypes.add("gif");
        allowedFileTypes.add("svg");
        allowedFileTypes.add("mp3");
        allowedFileTypes.add("wav");
        allowedFileTypes.add("flac");
        allowedFileTypes.add("avi");
        allowedFileTypes.add("flv");
        allowedFileTypes.add("mkv");
        allowedFileTypes.add("zip");
        allowedFileTypes.add("gz");
        allowedFileTypes.add("tar");
        allowedFileTypes.add("tgz");
        allowedFileTypes.add("7z");

        // 获取上传文件的类型
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf('.');
        String contentType = fileName.substring(lastIndex + 1);

        // 判断上传的文件类型是否符合要求
        if (!allowedFileTypes.contains(contentType)) {
            return AjaxResult.error("不允许此格式文件上传!");
        }

        // 判断上传的文件是否为图片
        MultipartFile multipartFile = file;
        UUID uuid = UUID.fastUUID();

        // 如果是图片则压缩
        long size = file.getSize();
        double fileSizeInMB = (double) size / (1024 * 1024);
        if (fileSizeInMB > 2) {
            if (contentType.contains("image")) {

                BufferedImage sourceImage = ImageIO.read(file.getInputStream());
                // 压缩图片（你可以根据需要调整压缩的质量）
                byte[] bytes = null;
                try {
                    bytes = compressPic(sourceImage, fileSizeInMB);
                    multipartFile = new MockMultipartFile("file", uuid + ".jpg", "image/jpeg", bytes);
                } catch (Exception e) {
                    multipartFile = file;
                }
            }
        }

        LocalDate localDate = LocalDate.now();
        String keyPrefix = localDate.getYear() + "/" + localDate.getMonthValue() + "/" + localDate.getDayOfMonth() + "/" + id;
        String key = "MER/original/" + keyPrefix + "/" + multipartFile.getOriginalFilename();
        InputStream inputStream = multipartFile.getInputStream();
        // 创建OSSClient实例
        OSSClientImpl ossClient = new OSSClientImpl(endpoint, accessKey, secretKey);
        // 简单文件上传
        ossClient.putObject(bucketName, key, inputStream);
        ossClient.setObjectAcl(bucketName, key, CannedAccessControlList.PublicReadWrite);
        String url = resourceUrl + "/" + key;
        AjaxResult result = new AjaxResult();
        result.put("url", url);
        result.put("name", multipartFile.getOriginalFilename());

        return result;
    }

    public static byte[] compressPic(BufferedImage image, double targetSizeInMB) throws IOException {
        ByteArrayOutputStream outArray = new ByteArrayOutputStream();
        // 指定写图片的方式为 jpg
        ImageWriter imgWriter = ImageIO.getImageWritersByFormatName("jpg").next();
        ImageWriteParam imgWriteParams = imgWriter.getDefaultWriteParam();
        // 要使用压缩，必须指定压缩方式为MODE_EXPLICIT
        imgWriteParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        float quality =1.0f;
        if(targetSizeInMB<2){
            quality=(float)1.0;
        }else {
            quality = (float) (2.0) / (float)targetSizeInMB;
        }
        // 这里指定压缩的程度，参数qality是取值0~1范围内，
        imgWriteParams.setCompressionQuality(quality);
        imgWriteParams.setProgressiveMode(ImageWriteParam.MODE_DISABLED);
        ColorModel.getRGBdefault();
        ColorModel colorModel = image.getColorModel();
        imgWriteParams.setDestinationType(new ImageTypeSpecifier(
                colorModel, colorModel.createCompatibleSampleModel(16, 16)));
        try {
            imgWriter.reset();
            // 必须先指定 out值，才能调用write方法, ImageOutputStream可以通过任何
            // OutputStream构造
            imgWriter.setOutput(ImageIO.createImageOutputStream(outArray));

            BufferedImage tag;
            tag = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_BGR);
            Graphics g = tag.getGraphics();
            g.drawImage(image, 0, 0, null); // 绘制缩小后的图
            g.dispose();
            image = tag;

            // 调用write方法，就可以向输入流写图片
            imgWriter.write(null, new IIOImage(image, null, null),
                    imgWriteParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return outArray.toByteArray();
    }

}
