package com.ruoyi.exams.mapper;

import com.ruoyi.exams.domain.UserAppraisalReport;

import java.util.List;

/**
 * 用户测评报告Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface UserAppraisalReportMapper {

    /**
     * 查询用户测评报告
     *
     * @param id 用户测评报告主键
     * @return 用户测评报告
     */
    public UserAppraisalReport selectUserAppraisalReportById(Long id);

    /**
     * 查询用户测评报告列表
     *
     * @param userAppraisalReport 用户测评报告
     * @return 用户测评报告集合
     */
    public List<UserAppraisalReport> selectUserAppraisalReportList(UserAppraisalReport userAppraisalReport);

    /**
     * 新增用户测评报告
     *
     * @param userAppraisalReport 用户测评报告
     * @return 结果
     */
    public int insertUserAppraisalReport(UserAppraisalReport userAppraisalReport);

    /**
     * 修改用户测评报告
     *
     * @param userAppraisalReport 用户测评报告
     * @return 结果
     */
    public int updateUserAppraisalReport(UserAppraisalReport userAppraisalReport);

    /**
     * 删除用户测评报告
     *
     * @param id 用户测评报告主键
     * @return 结果
     */
    public int deleteUserAppraisalReportById(Long id);

    /**
     * 批量删除用户测评报告
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserAppraisalReportByIds(Long[] ids);

}
