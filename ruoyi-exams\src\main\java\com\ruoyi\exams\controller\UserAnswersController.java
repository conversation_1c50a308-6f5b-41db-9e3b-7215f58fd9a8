package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.UserAnswers;
import com.ruoyi.exams.service.IUserAnswersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户答案Controller
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RestController
@RequestMapping("/answers")
public class UserAnswersController extends BaseController {

    @Autowired
    private IUserAnswersService userAnswersService;

    /**
     * 查询用户答案列表
     */
    @PreAuthorize("@ss.hasPermi('system:answers:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserAnswers userAnswers)
    {
        startPage();
        List<UserAnswers> list = userAnswersService.selectUserAnswersList(userAnswers);
        return getDataTable(list);
    }

    /**
     * 导出用户答案列表
     */
    @PreAuthorize("@ss.hasPermi('system:answers:export')")
    @Log(title = "用户答案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserAnswers userAnswers)
    {
        List<UserAnswers> list = userAnswersService.selectUserAnswersList(userAnswers);
        ExcelUtil<UserAnswers> util = new ExcelUtil<UserAnswers>(UserAnswers.class);
        util.exportExcel(response, list, "用户答案数据");
    }

    /**
     * 获取用户答案详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:answers:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userAnswersService.selectUserAnswersById(id));
    }

    /**
     * 新增用户答案
     */
    @PreAuthorize("@ss.hasPermi('system:answers:add')")
    @Log(title = "用户答案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserAnswers userAnswers)
    {
        return toAjax(userAnswersService.insertUserAnswers(userAnswers));
    }

    /**
     * 修改用户答案
     */
    @PreAuthorize("@ss.hasPermi('system:answers:edit')")
    @Log(title = "用户答案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserAnswers userAnswers)
    {
        return toAjax(userAnswersService.updateUserAnswers(userAnswers));
    }

    /**
     * 删除用户答案
     */
    @PreAuthorize("@ss.hasPermi('system:answers:remove')")
    @Log(title = "用户答案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userAnswersService.deleteUserAnswersByIds(ids));
    }

}
