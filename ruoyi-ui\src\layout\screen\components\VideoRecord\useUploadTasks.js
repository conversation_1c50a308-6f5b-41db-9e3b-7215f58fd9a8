/**
 * 上传任务管理模块
 * 提供上传任务队列管理和状态跟踪功能
 */
import { ref } from 'vue'

export function useUploadTasks(options = {}) {
  // 上传任务管理
  const uploadTasks = ref({}) // 存储上传任务 {topicId: {status, progress, blob, metadata}}
  const uploadingCount = ref(0) // 当前正在上传的任务数量
  const MAX_CONCURRENT_UPLOADS = options.maxConcurrentUploads || 2 // 最大并发上传数量
  const retryCount = ref(0) // 重试计数
  const maxRetries = options.maxRetries || 3 // 最大重试次数

  /**
   * 添加上传任务
   * @param {string} taskId - 任务ID
   * @param {Blob} rawBlob - 原始视频Blob
   * @param {Object} metadata - 元数据
   * @returns {Object} 任务对象
   */
  const addTask = (taskId, rawBlob, metadata) => {
    // 如果已存在相同ID的任务，先移除它（处理重复作答的情况）
    if (uploadTasks.value[taskId]) {
      console.log(`更新任务 ${taskId}`)
    }

    // 添加新任务到队列
    uploadTasks.value[taskId] = {
      status: 'pending', // pending | processing | uploading | completed | error
      progress: 0,
      rawBlob,
      metadata,
      createdAt: Date.now()
    }

    return uploadTasks.value[taskId]
  }

  /**
   * 处理上传队列
   * @param {Function} processBlob - 处理Blob的函数
   * @param {Function} uploadFn - 上传函数
   * @param {Function} emitEvent - 发送事件的函数
   */
  const processUploadQueue = async (processBlob, uploadFn, emitEvent) => {
    // 检查是否有正在进行的上传任务，如果达到最大并发数则等待
    if (uploadingCount.value >= MAX_CONCURRENT_UPLOADS) {
      // 设置一个短暂的延迟后再次尝试处理队列
      setTimeout(() => processUploadQueue(processBlob, uploadFn, emitEvent), 500)
      return
    }

    // 查找待处理的任务
    const pendingTasks = Object.entries(uploadTasks.value)
      .filter(([_, task]) => task.status === 'pending')
      .sort((a, b) => a[1].createdAt - b[1].createdAt) // 按创建时间排序

    if (pendingTasks.length === 0) {
      return // 没有待处理的任务
    }

    // 取出最早创建的任务
    const [taskId, task] = pendingTasks[0]

    // 更新任务状态
    uploadTasks.value[taskId].status = 'processing'
    uploadingCount.value++

    try {
      // 处理视频
      uploadTasks.value[taskId].status = 'processing'
      let processedBlob = await processBlob(task.rawBlob)

      // 上传视频
      uploadTasks.value[taskId].status = 'uploading'
      const response = await uploadFn(processedBlob, task.metadata, progress => {
        // 更新任务进度
        if (uploadTasks.value[taskId]) {
          uploadTasks.value[taskId].progress = progress
        }
      })

      // 更新任务状态为完成
      uploadTasks.value[taskId].status = 'completed'
      uploadTasks.value[taskId].response = response

      // 发出成功事件
      emitEvent({
        taskId,
        response,
        action: 'upload',
        metadata: task.metadata
      })
    } catch (error) {
      console.error(`任务 ${taskId} 处理失败:`, error)
      // 将任务标记为错误，但不中断答题流程
      uploadTasks.value[taskId].status = 'error'
      uploadTasks.value[taskId].error = error.message

      // 即使失败也发送成功事件，但标记为错误状态
      emitEvent({
        taskId,
        response: { url: `error_${taskId}`, error: error.message },
        action: 'upload',
        metadata: task.metadata,
        status: 'error'
      })
    } finally {
      // 减少正在上传的任务计数
      uploadingCount.value--

      // 继续处理队列中的下一个任务
      processUploadQueue(processBlob, uploadFn, emitEvent)
    }
  }

  /**
   * 上传单个任务
   * @param {Blob} blob - 视频Blob
   * @param {Object} metadata - 元数据
   * @param {Function} progressCallback - 进度回调
   * @param {Object} headers - 请求头
   * @param {string} uploadUrl - 上传URL
   * @returns {Promise<Object>} 上传响应
   */
  const uploadTask = async (blob, metadata, progressCallback, headers, uploadUrl) => {
    const formData = new FormData()
    formData.append('file', blob, `recording_${metadata.topicId}.mp4`) // 题目id命名，重名覆盖

    // 如果id是临时的，使用topicId作为文件夹名
    if (metadata.id === 'temp') {
      formData.append('id', metadata.topicId) // 使用题目ID作为文件夹
    } else {
      formData.append('id', metadata.id) // 考试记录id
    }

    try {
      return await new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        xhr.upload.onprogress = e => {
          if (e.lengthComputable) {
            const percent = Math.round((e.loaded / e.total) * 100)
            progressCallback(percent)
          }
        }

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(JSON.parse(xhr.responseText))
          } else {
            reject(new Error(`上传失败: ${xhr.status}`))
          }
        }

        xhr.onerror = () => reject(new Error('网络错误'))
        xhr.open('POST', uploadUrl)

        // 设置请求头
        Object.entries(headers).forEach(([k, v]) => xhr.setRequestHeader(k, v))

        xhr.send(formData)
      })
    } catch (error) {
      if (retryCount.value++ < maxRetries) {
        return uploadTask(blob, metadata, progressCallback, headers, uploadUrl)
      }
      throw error
    }
  }

  /**
   * 检查是否所有上传任务都已完成
   * @returns {boolean} 是否所有任务都已完成
   */
  const areAllUploadsComplete = () => {
    // 如果没有任务，返回true
    if (Object.keys(uploadTasks.value).length === 0) {
      return true
    }

    // 检查是否有任何任务未完成
    return !Object.values(uploadTasks.value).some(task => ['pending', 'processing', 'uploading'].includes(task.status))
  }

  /**
   * 获取总体上传进度
   * @returns {number} 总体进度（0-100）
   */
  const getOverallUploadProgress = () => {
    const tasks = Object.values(uploadTasks.value)
    if (tasks.length === 0) return 100

    // 计算所有任务的平均进度
    const totalProgress = tasks.reduce((sum, task) => {
      // 根据任务状态确定进度
      let taskProgress = 0
      switch (task.status) {
        case 'completed':
          taskProgress = 100
          break
        case 'error':
          taskProgress = 100 // 错误也视为完成
          break
        case 'uploading':
          taskProgress = task.progress
          break
        case 'processing':
          taskProgress = 30 // 处理中视为30%进度
          break
        case 'pending':
          taskProgress = 0
          break
      }
      return sum + taskProgress
    }, 0)

    return Math.round(totalProgress / tasks.length)
  }

  /**
   * 获取上传任务状态摘要
   * @returns {Object} 状态摘要
   */
  const getUploadStatusSummary = () => {
    const tasks = Object.values(uploadTasks.value)
    const total = tasks.length
    const completed = tasks.filter(t => t.status === 'completed').length
    const error = tasks.filter(t => t.status === 'error').length
    const inProgress = total - completed - error

    return {
      total,
      completed,
      error,
      inProgress,
      progress: getOverallUploadProgress()
    }
  }

  return {
    uploadTasks,
    uploadingCount,
    addTask,
    processUploadQueue,
    uploadTask,
    areAllUploadsComplete,
    getOverallUploadProgress,
    getUploadStatusSummary
  }
}
