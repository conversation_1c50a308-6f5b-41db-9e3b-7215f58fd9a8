<template>
  <div class="screen-container backdrop-blur">
    <!--人脸情绪感知测评    -->
    <carousel-wrapper v-if="facialEmotionList.length && id === '1'" v-model="currentIndex" :carousel-list="facialEmotionList">
      <div v-for="(item, index) in facialEmotionList" :key="index" class="carousel-item">
        <!-- 标题 -->
        <div class="topic flex">
          <p class="count shrink-0 m-0">{{ index + 1 }}</p>
          <p class="text m-0">{{ item.name }}</p>
        </div>

        <!-- 媒体展示区域 -->
        <div class="media-container">
          <!-- 选项区域 -->
          <div v-if="item.type === 'option'" class="video-wrapper">
            <div v-for="items in item.imgUrl" :key="items.id">
              <div style="display: flex; margin: 10px" @click="selectImg(items.id)">
                <p>{{ items.id }}:</p>
                <img class="video-wrapper-img" :src="items.img" />
              </div>
            </div>
          </div>
          <!-- 图片展示区域 -->
          <div v-else-if="item.type === 'image'" class="image-wrapper">
            <img :src="item.url" alt="情绪诱发图片" class="image-display" />
          </div>
          <!-- 模仿表情   -->
          <div v-else>
            <div v-if="emojoi">
              <!-- 视频预览（当没有拍照时显示） -->
              <video
                v-if="!photoData"
                ref="videoRef"
                autoplay
                playsinline
                muted
                class="video-preview"
              ></video>

              <!-- 拍摄结果（当有拍照时显示） -->
              <img
                v-if="photoData"
                :src="photoData"
                alt="拍摄结果"
                class="photo-result"
              />

              <!-- 错误信息显示 -->
              <div v-if="error" class="error-message">
                {{ error }}
              </div>

              <!-- 摄像头状态显示 -->
              <div class="camera-status" v-if="!photoData">
                <p v-if="!isCameraReady" style="color: #ff9800;">正在初始化摄像头...</p>
                <p v-else style="color: #4caf50;">摄像头已就绪</p>
              </div>
            </div>
            <div>
              <div class="screen-button" v-if="!emojoi" @click="beginPict()">开始</div>
              <div v-else class="button-group">
                <div class="screen-button" v-if="!photoData" @click="capturePhoto()">确定拍照</div>
                <div v-else class="button-group">
                  <div class="screen-button secondary" @click="retakePhoto()">重新拍照</div>
                  <div class="screen-button" @click="confirmPhoto()">确认照片</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 录音按钮 -->
        <div class="action-buttons" v-if="item.type === 'image'">
          <div class="evaluate-button" :class="{ evaluating: isEvaluating[index], evaluated: hasEvaluated[index] }" @click="toggleEvaluation(item, index)">
            {{ isEvaluating[index] ? '结束录音' : hasEvaluated[index] ? '已完成' : '开始录音' }}
          </div>

          <!-- <div v-if="canFinishTest" class="finish-button" @click="handleFinishTest">完成测试</div> -->
        </div>
      </div>
    </carousel-wrapper>

    <!-- 情绪辨别测试   -->
    <carousel-wrapper v-if="identfyEmotionList.length && id === '2'" v-model="currentIndex" :carousel-list="identfyEmotionList">
      <div v-for="(item, index) in identfyEmotionList" :key="index" class="carousel-item">
        <!-- 标题 -->
        <div class="topic flex">
          <p class="count shrink-0 m-0">{{ index + 1 }}</p>
          <p class="text m-0">{{ item.name }}</p>
        </div>

        <!-- 内容       -->
        <div v-if="item.type === 'content'">
          <div class="content-wrapper-top">
            <img :src="et01A" />
          </div>
          <div class="content-wrapper-bottom">
            <img :src="et01A" @click="next(item)" />
            <img :src="et01B" @click="next(item)" />
          </div>
        </div>
        <!-- 按钮展示区域 -->
        <div class="media-container">
          <div v-if="item.type === 'button'">
            <div class="screen-button" @click="next(item)">{{ item.buttonName }}</div>
          </div>
        </div>
      </div>
    </carousel-wrapper>

    <div v-if="emojoi" class="emojoi-top-img">
      <img :src="et01B" />
    </div>
    <div v-if="canFinishTest" class="finish-button" @click="handleFinishTest">完成测试</div>

    <!-- 加载动画 -->
    <div v-if="showLoading" class="loading-overlay">
      <div class="loading-modal">
        <div class="loading-spinner"></div>
        <h2>{{ loadingText }}</h2>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import CarouselWrapper from '@/views/screen/components/CarouselWrapper'
import { VideoPlay, RefreshRight } from '@element-plus/icons-vue'
import microExpression from '@/assets/video/demo/micro-expression.m4v'
import bgTest1 from '@/assets/images/screen/bg-test1.png'
import et01A from '@/assets/emotion/test01/A.png'
import et01B from '@/assets/emotion/test01/B.png'
import et01C from '@/assets/emotion/test01/C.png'
import et01E from '@/assets/emotion/test01/D.png'
import et01F from '@/assets/emotion/test01/E.png'
import et01G from '@/assets/emotion/test01/F.png'
import ep03 from '@/assets/emotion/test01/03.jpeg'
//import ep04 from '@/assets/emotion/pictures/04.png'

import bgTest2 from '@/assets/images/screen/bg-test2.png'

const router = useRouter()
const currentIndex = ref(0)
const showLoading = ref(false)
const emojoi = ref(false)
const videoRef = ref(null)
const isCameraReady = ref(false)
const photoData = ref(null)
const error = ref(null)
let mediaStream = null
const loadingText = ref('请稍候...')
const videoRefs = ref({})

// 播放状态
const isPlaying = ref({})
const isVideoEnded = ref({})

// 评价状态
const isEvaluating = ref({})
const hasEvaluated = ref({})
const evaluationResults = ref({})

// 测评id
const route = useRoute()
const id = ref(route.query.id)

console.log(id.value)

// 人脸情绪感知测评
const facialEmotionList = ref([
  {
    id: 1,
    name: '这里有一些表情各异的人脸，请根据您对生活的总体感受说出最接近的表情。',
    type: 'option',
    imgUrl: [
      { id: 'A', img: et01A },
      { id: 'B', img: et01B },
      { id: 'C', img: et01C },
      { id: 'D', img: et01E },
      { id: 'E', img: et01F },
      { id: 'F', img: et01G }
    ]
  },
  { id: 2, name: '请您仔细观察我的面部表情，并且尽力模仿我做相应的表情。' },
  { id: 3, name: '请您根据下图的内容，把故事补充完整，要求语言通顺，内容具体。准备好后可点击按钮开始录音。', type: 'image', url: ep03 }
])

// 情绪辨别测试
const identfyEmotionList = ref([
  {
    id: 1,
    name: '提示:本实验有两种任务，表情辨别任务和图形辨别人物稍后会详细说明这两种任务，请根据不同的指导与相应地反应，注意每一部分任务的变化明白了请按下一步继续',
    type: 'button',
    buttonName: '下一步'
  },
  {
    id: 2,
    name: '该任务中会同事呈现三张面孔，请五秒内判断下方的两张面孔中哪一张与上访的面孔表情相同冰点击选择该张面孔，没有相同的可不选',
    type: 'button',
    buttonName: '开始测评'
  },
  { id: 3, name: '请您5秒内判断屏幕上访的面孔表情与下方的哪张图片一致，点击相应的图片', type: 'content' },
  {
    id: 4,
    name: '该任务中会同时呈现三个图形，请在五秒内判断下方的两个图形中哪一个与上方的图形方向一样冰点击选择该图形，没有相同的不选',
    type: 'button',
    buttonName: '开始测评'
  },
  { id: 5, name: '请您5秒内判断屏幕上访的图形与下方的哪张图片一致，点击相应的图片', type: 'content' },
  { id: 6, name: '恭喜您已完成测试，请等待医生后台评分', type: 'button', buttonName: '结束' }
])

// 下一步
const next = item => {
  if (item.id === identfyEmotionList.value.length) {
    router.push({ name: 'ScreenPreIndex' })
  } else {
    currentIndex.value = item.id
  }
}

// 示例素材列表 - 使用已有的资源
const materialList = ref([
  { id: 1, name: '快乐情绪视频', type: 'video', url: microExpression, emotion: 'happy' },
  { id: 2, name: '悲伤情绪视频', type: 'video', url: microExpression, emotion: 'sad' }
  //{ id: 3, name: '请观察图片后给出评价', type: 'image', url: ep01, emotion: 'angry' }
])

// 是否可以完成测试（至少评价了一个素材）
const canFinishTest = computed(() => {
  return Object.values(hasEvaluated.value).some(value => value === true)
})

// 定义事件
const emit = defineEmits(['startRecording', 'stopRecording', 'handlePlay'])

// 切换视频播放状态
const toggleVideoPlay = (_item, index) => {
  // 使用 videoRefs 获取视频元素
  const video = videoRefs.value[index]

  if (!video) {
    console.error('找不到视频元素:', index)
    return
  }

  console.log('尝试播放/暂停视频:', index, video)

  try {
    if (isVideoEnded.value[index]) {
      // 如果视频已结束，重新播放
      video.currentTime = 0
      isVideoEnded.value[index] = false
    }

    if (isPlaying.value[index]) {
      // 暂停视频
      video.pause()
      console.log('视频已暂停')
    } else {
      // 播放视频 - 使用 Promise 处理
      const playPromise = video.play()

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('视频开始播放')
            isPlaying.value[index] = true
          })
          .catch(error => {
            console.error('视频播放失败:', error)
            // 尝试静音播放（解决自动播放策略问题）
            video.muted = true
            video.play().catch(err => {
              console.error('静音播放也失败:', err)
            })
          })
      }
    }
  } catch (error) {
    console.error('视频操作错误:', error)
  }
}

// 视频播放事件处理
const handleVideoPlay = (_item, index) => {
  isPlaying.value[index] = true
}

// 视频暂停事件处理
const handleVideoPause = (_item, index) => {
  isPlaying.value[index] = false
}

// 视频结束事件处理
const handleVideoEnded = (_item, index) => {
  console.log(`视频 ${index} 播放结束`)
  isPlaying.value[index] = false
  isVideoEnded.value[index] = true

  // 强制更新状态
  setTimeout(() => {
    isVideoEnded.value = { ...isVideoEnded.value }
  }, 0)
}

// 视频错误事件处理
const handleVideoError = (item, index) => {
  console.error('视频播放错误:', item)
  isPlaying.value[index] = false
}

// 监听轮播索引变化
watch(currentIndex, (newIndex, oldIndex) => {
  console.log(`轮播从 ${oldIndex} 切换到 ${newIndex}`)

  // 停止所有视频播放
  Object.keys(videoRefs.value).forEach(key => {
    const video = videoRefs.value[key]
    if (video) {
      video.pause()
      isPlaying.value[key] = false
    }
  })
})

// 切换评价状态
const toggleEvaluation = (item, index) => {
  if (isEvaluating.value[index]) {
    // 结束评价
    stopEvaluation(item, index)
  } else {
    // 开始评价
    startEvaluation(item, index)
  }
}

// 开始评价
const startEvaluation = (item, index) => {
  isEvaluating.value[index] = true

  // 调用父组件的开始录制方法
  emit('startRecording')

  console.log(`开始评价素材: ${item.name}`)
}

// 选择图片表情
const selectImg = item => {
  currentIndex.value++
}

// 修改开始按钮逻辑
const beginPict = async () => {
  console.log('点击开始按钮')
  emojoi.value = true

  // 等待DOM更新完成
  await nextTick()

  // 添加额外的延迟确保DOM完全渲染
  setTimeout(() => {
    console.log('准备初始化摄像头')
    initCamera()
  }, 100)
}

const initCamera = async () => {
  console.log('开始初始化摄像头')

  // 检查视频元素是否存在
  if (!videoRef.value) {
    console.error('视频元素不存在，videoRef.value:', videoRef.value)
    error.value = '视频元素未找到，请稍后重试'
    return
  }

  console.log('视频元素已找到:', videoRef.value)

  try {
    // 检查浏览器是否支持getUserMedia
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('浏览器不支持摄像头访问')
    }

    const constraints = {
      video: {
        facingMode: 'user',
        width: { ideal: 1280 },
        height: { ideal: 720 }
      }
    }

    console.log('请求摄像头权限，约束条件:', constraints)

    // 请求摄像头权限
    mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
    console.log('成功获取媒体流:', mediaStream)

    // 检查媒体流是否有效
    if (!mediaStream || mediaStream.getTracks().length === 0) {
      throw new Error('获取的媒体流无效')
    }

    // 设置视频源
    videoRef.value.srcObject = mediaStream
    console.log('已设置视频源到video元素')

    // 监听视频元素事件
    videoRef.value.onloadedmetadata = () => {
      console.log('视频元数据已加载')
      isCameraReady.value = true
    }

    videoRef.value.oncanplay = () => {
      console.log('视频可以播放')
      isCameraReady.value = true
    }

    videoRef.value.onerror = (e) => {
      console.error('视频元素错误:', e)
      error.value = '视频播放错误'
    }

    // 尝试播放视频
    try {
      await videoRef.value.play()
      console.log('视频开始播放')
    } catch (playError) {
      console.warn('自动播放失败，这是正常的:', playError)
      // 自动播放失败是正常的，用户需要手动交互
    }

    console.log('摄像头初始化完成')
  } catch (err) {
    console.error('摄像头初始化错误:', err)
    error.value = `摄像头初始化失败: ${err.message}`

    // 提供更具体的错误信息
    if (err.name === 'NotAllowedError') {
      error.value = '摄像头权限被拒绝，请允许访问摄像头'
    } else if (err.name === 'NotFoundError') {
      error.value = '未找到摄像头设备'
    } else if (err.name === 'NotReadableError') {
      error.value = '摄像头被其他应用占用'
    }

    stopCamera()
  }
}

// 修改拍照逻辑
const capturePhoto = () => {
  console.log('开始拍照')
  const video = videoRef.value

  // 新增验证逻辑
  if (!video) {
    error.value = '视频元素未找到'
    console.error('视频元素未找到')
    return
  }

  if (video.videoWidth === 0 || video.videoHeight === 0) {
    error.value = '视频尚未准备好，请等待画面出现'
    console.error('视频尺寸无效:', video.videoWidth, 'x', video.videoHeight)
    return
  }

  console.log('视频尺寸:', video.videoWidth, 'x', video.videoHeight)

  const canvas = document.createElement('canvas')
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight

  const ctx = canvas.getContext('2d')

  // 镜像翻转，使拍照结果与预览一致
  ctx.translate(canvas.width, 0)
  ctx.scale(-1, 1)

  try {
    ctx.drawImage(video, 0, 0)

    // 将canvas转换为base64图片数据
    photoData.value = canvas.toDataURL('image/jpeg', 0.8)
    console.log('拍照成功，图片数据长度:', photoData.value.length)

    // 清除错误信息
    error.value = null

    // 可选：停止摄像头（如果不需要继续使用）
    // stopCamera()

  } catch (e) {
    error.value = `截图失败: ${e.message}`
    console.error('截图失败:', e)
    return
  }
}

// 重新拍照
const retakePhoto = () => {
  console.log('重新拍照')
  photoData.value = null
  error.value = null
  // 摄像头流应该还在运行，不需要重新初始化
}

// 确认照片
const confirmPhoto = () => {
  console.log('确认照片')
  // 这里可以添加确认照片后的逻辑，比如进入下一步
  currentIndex.value++

  // 停止摄像头
  stopCamera()

  // 重置状态
  emojoi.value = false
  photoData.value = null
  isCameraReady.value = false
  error.value = null
}

// 清理资源
const stopCamera = () => {
  console.log('停止摄像头')
  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop())
    mediaStream = null
  }
}

// 结束评价
const stopEvaluation = (item, index) => {
  isEvaluating.value[index] = false
  hasEvaluated.value[index] = true

  // 调用父组件的停止录制方法
  emit('stopRecording', {
    id: `emotion_${item.id}`,
    materialId: item.id,
    callback: function (res) {
      // 保存评价结果
      evaluationResults.value[item.id] = {
        materialId: item.id,
        materialName: item.name,
        materialType: item.type,
        materialEmotion: item.emotion,
        videoUrl: res.url,
        timestamp: new Date().toISOString()
      }

      console.log(`素材 ${item.name} 的评价已保存，视频URL: ${res.url}`)
    }
  })
}

// 完成测试
const handleFinishTest = () => {
  showLoading.value = true
  loadingText.value = '正在处理评价结果...'

  // 模拟提交评价结果
  setTimeout(() => {
    console.log('评价结果:', evaluationResults.value)
    showLoading.value = false

    // 跳转回主页
    router.push({ name: 'ScreenPreIndex' })
  }, 2000)
}

// 组件挂载
onMounted(() => {
  // 触发父组件的数字人视频播放
  emit('handlePlay', 'standby')

  // 初始化状态
  materialList.value.forEach((_item, index) => {
    isPlaying.value[index] = false
    isVideoEnded.value[index] = false
    isEvaluating.value[index] = false
    hasEvaluated.value[index] = false
  })

  // 确保视频元素加载完成后设置初始状态
  setTimeout(() => {
    // 强制更新状态对象，确保响应式
    isPlaying.value = { ...isPlaying.value }
    isVideoEnded.value = { ...isVideoEnded.value }
  }, 500)
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 如果正在评价，停止评价
  materialList.value.forEach((item, index) => {
    if (isEvaluating.value[index]) {
      stopEvaluation(item, index)
    }
  })
  // 清理视频
  stopCamera()
})
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;

  .carousel-item {
    padding: 70px 140px;
    flex: 0 0 100%;
    min-width: 100%;
    box-sizing: border-box;
    backface-visibility: hidden;
  }

  .content-wrapper-top {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 300px;
    }
  }
  .content-wrapper-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    img {
      width: 300px;
    }
  }

  /* 题目样式 */
  .topic {
    column-gap: 41px;
    .count {
      width: 96px;
      height: 120px;
      background-image: url(@/assets/images/screen/bg-count.png);
      background-size: 100% 100%;
      padding-top: 36px;
      text-align: center;
      font-family: Archivo Black;
      font-size: 56px;
      font-weight: normal;
      line-height: 56px;
      color: #0d478e;
    }
    .text {
      margin-top: 14px;
      font-family: AlibabaPuHuiTiBold;
      font-size: 54px;
      line-height: 106px;
    }
  }

  // #region 按钮提出来
  .finish-button {
    background-color: #005eff;
    color: #ffffff;
    border: 4px solid #005eff;
    position: absolute;
    top: -160px;
    right: 60px;
  }

  .emojoi-top-img {
    position: absolute;
    top: -6rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .evaluate-button,
  .finish-button {
    padding: 20px 40px;
    border-radius: 20px;
    font-family: AlibabaPuHuiTiBold;
    font-size: 48px;
    cursor: pointer;
    transition: all 0.2s;

    &:active {
      transform: scale(0.96);
    }
  }
  // #endregion
  /* 媒体容器样式 */
  .media-container {
    margin-top: 80px;
    display: flex;
    justify-content: center;

    .video-wrapper {
      display: flex;
      flex-wrap: wrap;
    }
    .video-wrapper-img {
      width: 1.5rem;
    }
    .screen-button {
      background: #ffffff;
      color: #005eff;
      padding: 16px 40px;
      border: 4px solid #005eff;
      border-radius: 30px;
      font-size: 0.35rem;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      text-align: center;
      margin: 0 10px;

      &.secondary {
        background: #f5f5f5;
        color: #666;
        border-color: #ccc;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .button-group {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
    }
    .video-preview {
      width: 640px;
      height: 480px;
      transform: scaleX(-1); /* 镜像翻转 */
      border: 2px solid #fff;
      border-radius: 8px;
    }

    .photo-result {
      width: 640px;
      height: 480px;
      border: 2px solid #fff;
      border-radius: 8px;
      object-fit: cover;
    }

    .error-message {
      margin-top: 20px;
      padding: 15px;
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ef5350;
      border-radius: 8px;
      font-size: 16px;
      text-align: center;
    }

    .camera-status {
      margin-top: 15px;
      text-align: center;
      font-size: 18px;
      font-weight: bold;
    }

    .image-wrapper {
      position: relative;
      width: 100%;
      height: 600px;
      background-color: #fafbfe;
      border-radius: 32px;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;

      .video-player,
      .image-display {
        max-width: 100%;
        max-height: 100%;
      }

      .video-controls {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.3);

        .control-icon {
          font-size: 120px;
          color: white;
          filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.5));
        }
      }
    }
  }

  /* 操作按钮样式 */
  .action-buttons {
    margin-top: 60px;
    display: flex;
    justify-content: center;
    gap: 40px;

    .evaluate-button {
      background-color: #ffffff;
      color: #005eff;
      border: 4px solid #005eff;

      &.evaluating {
        background-color: #ff5252;
        color: #ffffff;
        border-color: #ff5252;
        animation: pulse 1.5s infinite;
      }

      &.evaluated {
        background-color: #4caf50;
        color: #ffffff;
        border-color: #4caf50;
      }
    }
  }
}

/* 加载动画样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-modal {
  background-color: white;
  border-radius: 32px;
  padding: 60px;
  width: 80%;
  max-width: 1200px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

  h2 {
    font-family: AlibabaPuHuiTiBold;
    font-size: 72px;
    color: #005eff;
    margin-top: 40px;
  }

  .loading-spinner {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border: 16px solid #f3f3f3;
    border-top: 16px solid #005eff;
    border-radius: 50%;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
</style>
