/**
 * 变焦控制模块
 * 提供摄像头变焦和缩放功能
 */
import { ref } from 'vue'

export function useZoomControl() {
  // 前端变焦相关
  const zoomMin = ref(1)
  const zoomMax = ref(3)
  const zoomStep = ref(0.01)
  const zoomValue = ref(1)
  const showZoomSlider = ref(false)

  /**
   * 切换变焦滑块显示状态
   */
  const toggleZoomSlider = () => {
    showZoomSlider.value = !showZoomSlider.value
  }

  /**
   * 初始化变焦参数
   */
  const initZoom = () => {
    zoomMin.value = 1
    zoomMax.value = 3
    zoomStep.value = 0.01
    zoomValue.value = 1
  }

  /**
   * 设置变焦值
   * @param {number} value - 变焦值
   */
  const setZoom = value => {
    if (value >= zoomMin.value && value <= zoomMax.value) {
      zoomValue.value = value
    }
  }

  return {
    zoomMin,
    zoomMax,
    zoomStep,
    zoomValue,
    showZoomSlider,
    toggleZoomSlider,
    initZoom,
    setZoom
  }
}
