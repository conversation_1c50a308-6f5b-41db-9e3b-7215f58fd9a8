<template>
  <div class="screen-container backdrop-blur">
    <!-- 介绍语句 -->
    <div class="introduction">恭喜您已完成测试，是否查看结果</div>
    <!-- 按钮 -->
    <div class="button-container flex flex-col items-center">
      <div class="screen-button bg-[#005EFF] text-[#FFFFFF]" @click="router.push({ name: 'ScreenResult', query: { id: route.query.id } })">查看结果</div>
      <!-- <div class="screen-button bg-[#005EFF] text-[#FFFFFF]" @click="router.push({ name: 'ScreenHistory'})">历史测评</div> -->
      <div class="screen-button bg-[#EAF3FF] text-[#005EFF]" @click="router.push({ name: 'ScreenPreIndex'})">结 束</div>
    </div>
  </div>
</template>

<script setup>
const router = useRouter(),
  route = useRoute()

// #region 视频播放
const emit = defineEmits(['handlePlay'])
onMounted(() => {
  emit('handlePlay', 'prompt4')
})
// #endregion
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  .introduction {
    padding-top: 116px;
    font-family: AlibabaPuHuiTiBold;
    font-size: 72px;
    font-weight: 500;
    line-height: 72px;
    text-align: center;
    letter-spacing: -0.05em;
    color: #3d3d3d;
  }
  // 按钮
  .button-container {
    margin-top: 208px;
    gap: 72px;
    // padding-top: 200px;
    .screen-button {
      width: 1283px;
      height: 160px;
      border-radius: 32px;
      font-family: AlibabaPuHuiTiBold;
      font-size: 54px;
      line-height: 160px;
      text-align: center;
      border: 4px solid #005eff;
    }
  }
}
</style>
