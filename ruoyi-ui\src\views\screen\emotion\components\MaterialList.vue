<template>
  <div class="material-list">
    <!-- <div class="material-header">
      <h2 class="title">{{ title }}</h2>
      <div class="controls" v-if="showControls">
        <el-button type="primary" @click="playAll">播放全部</el-button>
      </div>
    </div> -->

    <div class="material-grid">
      <div v-for="(item, index) in materials" :key="index" class="material-item" @click="handleItemClick(item, index)">
        <!-- 视频素材 -->
        <div v-if="item.type === 'video'" class="video-item">
          <div class="thumbnail">
            <img :src="item.thumbnail || defaultVideoThumbnail" alt="视频缩略图" />
            <div class="play-icon">
              <el-icon><video-play /></el-icon>
            </div>
          </div>
          <div class="info">
            <div class="name">{{ item.name }}</div>
            <div class="duration">{{ formatDuration(item.duration) }}</div>
          </div>
        </div>

        <!-- 图片素材 -->
        <div v-else-if="item.type === 'image'" class="image-item">
          <div class="thumbnail">
            <img :src="item.url" alt="图片" />
          </div>
          <div class="info">
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { VideoPlay } from '@element-plus/icons-vue'

const props = defineProps({
  // 素材列表标题
  title: {
    type: String,
    default: '素材列表'
  },
  // 素材列表
  materials: {
    type: Array,
    default: () => []
  },
  // 是否显示控制按钮
  showControls: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['play', 'select'])

// 默认视频缩略图
const defaultVideoThumbnail = ref('/src/assets/images/screen/video-thumbnail.png')

// 格式化时长
const formatDuration = seconds => {
  if (!seconds) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 处理素材点击
const handleItemClick = (item, index) => {
  emit('select', item, index)
  if (item.type === 'video') {
    emit('play', item, index)
  }
}

// 播放全部
const playAll = () => {
  emit('play', props.materials[0], 0, true) // 第三个参数表示播放全部
}
</script>

<style lang="scss" scoped>
.material-list {
  width: 100%;
  padding: 20px;

  .material-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 60px;
      color: #033682;
      margin: 0;
    }

    .controls {
      :deep(.el-button) {
        font-size: 40px;
        padding: 20px 40px;
        height: auto;
      }
    }
  }

  .material-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 40px;

    .material-item {
      cursor: pointer;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s;

      .thumbnail {
        position: relative;
        width: 100%;
        height: 300px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 80px;
          height: 80px;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;

          :deep(.el-icon) {
            font-size: 40px;
            color: white;
          }
        }
      }

      .info {
        padding: 20px;

        .name {
          font-size: 40px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #333;
        }

        .duration {
          font-size: 30px;
          color: #666;
        }
      }
    }
  }
}
</style>
