<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.UserExamsMapper">

    <resultMap type="UserExams" id="UserExamsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="examId"    column="exam_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="score"    column="score"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserExamsVo">
        select id, user_id, exam_id, start_time, end_time, score, status, create_by, create_time, update_by, update_time from user_exams
    </sql>

    <select id="selectUserExamsList" parameterType="UserExams" resultMap="UserExamsResult">
        <!-- <include refid="selectUserExamsVo"/> -->
        select a.id, a.user_id, a.exam_id, b.name examName, a.start_time, a.end_time, a.score, a.status, a.create_by, a.create_time, a.update_by, a.update_time
        from user_exams a left join exams b on a.exam_id = b.id
        <where>
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="examId != null "> and a.exam_id = #{examId}</if>
            <if test="startTime != null "> and a.start_time = #{startTime}</if>
            <if test="endTime != null "> and a.end_time = #{endTime}</if>
            <if test="score != null "> and a.score = #{score}</if>
            <if test="status != null "> and a.status = #{status}</if>
        </where>
    </select>

    <select id="selectUserExamsById" parameterType="Long" resultMap="UserExamsResult">
        <include refid="selectUserExamsVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserExams" parameterType="UserExams" useGeneratedKeys="true" keyProperty="id">
        insert into user_exams
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="examId != null">exam_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="score != null">score,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="examId != null">#{examId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="score != null">#{score},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUserExams" parameterType="UserExams">
        update user_exams
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="examId != null">exam_id = #{examId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="score != null">score = #{score},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserExamsById" parameterType="Long">
        delete from user_exams where id = #{id}
    </delete>

    <delete id="deleteUserExamsByIds" parameterType="String">
        delete from user_exams where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryEvaluationRecord" parameterType="UserExams" resultType="map">
        SELECT
	        b.id questionId,
	        b.question_text questionText,
	        b.question_type questionType,
	        b.score_order scoreOrder,
	        b.reverse_score reverseScore,
	        c.id optionId,
	        c.option_text optionText,
	        c.score,
	        c.sort_order sortOrder,
	        CASE
                WHEN d.option_id IS NOT NULL THEN 1
                ELSE 0
            END AS isSelected
        FROM
	        user_exams s
	    LEFT JOIN exams a on s.exam_id = a.id
	    LEFT JOIN questions b ON a.id = b.exam_id
	    LEFT JOIN options c ON b.id = c.question_id
	    LEFT JOIN user_answers d ON s.id = d.user_exam_id and b.id = d.question_id and c.id = d.option_id
        WHERE
	        s.id = #{id}
        ORDER BY
	        b.score_order,
	        c.sort_order
    </select>

    <select id="historyList" parameterType="UserExams" resultMap="UserExamsResult">
        select a.id, a.user_id, a.exam_id, b.name examName, a.start_time, a.end_time, a.score, a.status, a.create_by, a.create_time, a.update_by, a.update_time
        from user_exams a left join exams b on a.exam_id = b.id
        <where>
            <if test="userId != null "> and a.user_id = #{userId}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="percentageList" parameterType="UserExams">
        SELECT emotion, COUNT(*) AS num, ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_appraisal_report WHERE exam_id = 125)),2) AS percentage, max(emotion_score) maxEmotionScore, min(emotion_score) minEmotionScore
        FROM user_appraisal_report WHERE exam_id = #{examId} GROUP BY emotion order by percentage desc
    </select>

</mapper>
