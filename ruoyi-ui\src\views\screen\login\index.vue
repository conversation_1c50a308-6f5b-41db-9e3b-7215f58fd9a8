<template>
  <div class="screen-container backdrop-blur">
    <!-- 提示语 -->
    <div class="prompt">{{ prompt }}</div>

    <div v-if="route.query.type === 'qrcode'" class="qrcode-container m-auto flex justify-center items-center">
      <img :src="DataURL" alt="" />
    </div>
    <div v-else class="username-container flex justify-center items-center">
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form flex flex-col">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
            <template #prefix><img src="@/assets/images/screen/icon-form_username.png" class="input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
            <template #prefix><img src="@/assets/images/screen/icon-form_pwd.png" class="input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 59%" @keyup.enter="handleLogin">
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" />
          </div>
        </el-form-item>
        <el-form-item style="width: 100%">
          <div class="screen-button" :loading="loading" type="primary" style="width: 100%" @click.prevent="handleLogin">
            <span v-if="!loading">确 认</span>
            <span v-else>登 录 中...</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import QRCode from 'qrcode'

import { getCodeImg } from '@/api/login'
import useUserStore from '@/store/modules/user'

let prompt // 提示语

const DataURL = ref('') // 二维码图片地址

// #region 用户名登录
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// const loginForm = ref({ username: 'admin', password: 'admin123', code: '', uuid: '' })
const loginForm = ref({ username: '', password: '', code: '', uuid: '' })
const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

const codeUrl = ref('')
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(false)
// #endregion

if (route.query.type === 'qrcode') {
  // type=qrcode 扫码登录
  prompt = '请用微信扫描下面二维码进行身份认证'
  QRCode.toDataURL('I am a pony!', { width: 360, height: 360, errorCorrectionLevel: 'H', margin: 3 }, function (err, url) {
    DataURL.value = url
  })
} else {
  // type=username 用户名登录
  prompt = '请输入用户名密码进行身份验证'
  // getCode()
}
// #region 视频播放
const emit = defineEmits(['handlePlay'])
onMounted(() => {
  emit('handlePlay', route.query.type === 'qrcode' ? 'prompt1' : 'prompt2')
})
// #endregion

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(() => {
          router.push({ name: 'ScreenPreIndex' })
        })
        .catch(() => {
          loading.value = false
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode()
          }
        })
    }
  })
}

function getCode() {
  getCodeImg().then(res => {
    // captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}
</script>
<style lang="scss" scoped>
.screen-container {
  .prompt {
    padding-top: 120px;
    margin-bottom: 121px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 80px;
    text-align: center;
    color: #033682;
  }
  .qrcode-container {
    width: 520px;
    height: 560px;
    background-image: url('@/assets/images/screen/bg-qr_code.png');
    background-size: 100% 100%;
    img {
      width: 360px;
      height: 360px;
      border-radius: 32px;
      background: #ffffff;
    }
  }
  .username-container {
    :deep(.login-form) {
      width: 1000px;
      gap: 60px;
      .el-form-item {
        margin-bottom: 0;
        // 错误提示
        .el-form-item__error {
          font-size: 38px;
        }
        .el-input {
          height: 150px;
          .el-input__wrapper {
            border-radius: 32px;
            padding-left: 41px;
            input {
              height: 150px;
              font-size: 54px;
              font-weight: 600;
              color: #3d3d3d;
            }
          }
        }
        .input-icon {
          width: 60px;
          height: 60px;
          margin-right: 50px;
        }
        // 验证码
        .login-code {
          width: 41%;
          height: 150px;
          float: right;
          img {
            cursor: pointer;
            vertical-align: middle;
            height: 150px;
            padding-left: 12px;
          }
        }

        .screen-button {
          height: 150px;
          border-radius: 32px;
          background: #005eff;
          font-size: 54px;
          line-height: 150px;
          text-align: center;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
