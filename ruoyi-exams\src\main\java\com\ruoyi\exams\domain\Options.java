package com.ruoyi.exams.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 选项对象 options
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
public class Options extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 题目id */
    @Excel(name = "题目id")
    private Long questionId;

    /** 名称 */
    @Excel(name = "名称")
    private String optionText;

    /** 是否正确答案 */
    @Excel(name = "是否正确答案")
    private Integer isCorrect;

    /** 分数 */
    @Excel(name = "分数")
    private BigDecimal score;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

}
