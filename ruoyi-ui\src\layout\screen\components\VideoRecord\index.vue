<template>
  <!--
    摄像头录制组件，支持人脸检测和变焦功能
    1. 支持摄像头变焦（zoom），仅在设备支持时显示滑块
    2. 详细注释见 script setup 部分
  -->
  <div v-if="allowsRecord" class="video-recorder">
    <!-- 视频预览 -->
    <div class="video-container">
      <!--
        1. 如果摄像头支持zoom，滑块控制硬件变焦
        2. 如果摄像头不支持zoom，滑块控制前端scale实现放大
        3. :style 动态绑定scale样式，canvas同步缩放
      -->
      <video
        ref="preview"
        autoplay
        muted
        playsinline
        class="video-element"
        :style="{ transform: `translateX(-50%) scale(-${zoomValue}, ${zoomValue})`, transformOrigin: 'center center' }"
        @click="toggleZoomSlider"
      ></video>
      <!-- 增加canvas用于人脸关键点绘制 -->
      <canvas
        ref="faceCanvas"
        class="face-canvas"
        :style="{ transform: `translateX(-50%) scale(-${zoomValue}, ${zoomValue})`, transformOrigin: 'center center' }"
      ></canvas>
    </div>
    <!-- 变焦滑块，默认隐藏，点击视频显示，再次点击视频隐藏 -->
    <div v-if="showZoomSlider" class="zoom-slider">
      <span>摄像头变焦</span>
      <el-slider v-model="zoomValue" :min="zoomMin" :max="zoomMax" :step="zoomStep" :show-tooltip="true" :format-tooltip="val => `变焦: ${val.toFixed(2)}x`" />
    </div>
    <!-- 状态提示 -->
    <div class="status-indicator">
      <span v-if="faceDetecting && !faceDetected" style="color: #ff5252">⚠️ 没有检测到人脸</span>
    </div>
    <!-- 答题太快提示 -->
    <div v-if="showTooFastTip" class="too-fast-tip" style="color: #ff9800; position: absolute; bottom: 10px; left: 0; width: 100%; text-align: center; z-index: 10">
      请停留至少 {{ minAnswerSeconds }} 秒
    </div>
  </div>
</template>

<script setup>
import { getToken } from '@/utils/auth'
import { ref, onMounted, nextTick } from 'vue'

// 导入功能模块
import { useFaceDetection } from './useFaceDetection'
import { useVideoRecording } from './useVideoRecording'
import { useVideoProcessing } from './useVideoProcessing'
import { useUploadTasks } from './useUploadTasks'
import { useZoomControl } from './useZoomControl'

// 组件属性
const props = defineProps({
  // 操作类型控制
  action: {
    type: String,
    default: 'upload', // 'upload' | 'download'
    validator: v => ['upload', 'download'].includes(v)
  },
  // 上传配置
  uploadUrl: { type: String, default: import.meta.env.VITE_APP_BASE_API + '/fileUpload/simpleUpload' },
  headers: { type: Object, default: () => ({ Authorization: 'Bearer ' + getToken() }) },

  // 录制配置 - 使用 16:9 的比例 (1260:690)
  video: { type: Object, default: () => ({ width: 1260, height: 690 }) },
  audio: { type: [Boolean, Object], default: true },

  // 重试配置
  maxRetries: { type: Number, default: 3 }
})

// 事件
const emit = defineEmits(['start', 'progress', 'success', 'error'])

// 视频元素引用
const preview = ref(null)
const faceCanvas = ref(null)

// 答题时间控制
const minAnswerSeconds = 4 // 最小答题停留秒数

// 初始化各功能模块
const { faceDetected, faceDetecting, initFaceDetection } = useFaceDetection()
const { mediaStream, status, allowsRecord, showTooFastTip, initStream, start: startRecording, stop: stopRecording } = useVideoRecording({ minAnswerSeconds })
const { isFFmpegLoaded, processForUpload, downloadVideo } = useVideoProcessing()
const { addTask, processUploadQueue, uploadTask, areAllUploadsComplete, getOverallUploadProgress, getUploadStatusSummary } = useUploadTasks({
  maxRetries: props.maxRetries,
  maxConcurrentUploads: 2
})
const { zoomMin, zoomMax, zoomStep, zoomValue, showZoomSlider, toggleZoomSlider, initZoom } = useZoomControl()

/**
 * 公共的 video/canvas 初始化逻辑
 * 设置canvas尺寸、加载模型、启动检测循环
 */
const handleVideoReady = async () => {
  await initFaceDetection(preview.value, faceCanvas.value)
}

// 组件初始化
onMounted(async () => {
  try {
    // 初始化摄像头流
    await nextTick()
    await initStream(props.video, props.audio, preview.value, handleVideoReady)

    // 初始化变焦参数
    initZoom()
  } catch (error) {
    handleError(new Error('初始化失败: ' + error.message))
  }
})

// 开始录制
const start = async () => {
  const success = await startRecording()
  if (success) {
    emit('start')
  }
}

// 停止录制并创建上传任务
const stop = async (metadata = {}) => {
  if (!allowsRecord.value) {
    // 摄像头访问失败 => 不执行上传操作，但仍然触发回调以继续答题流程
    metadata?.callback({})
    return
  }

  // 停止录制，获取原始视频数据
  const rawBlob = await stopRecording()
  if (!rawBlob) {
    // 如果停止录制失败或答题时间不足，不触发回调
    return
  }

  // 重要：立即触发回调，让用户可以继续答题
  // 传递一个临时URL，后续会被真实URL替换
  const tempResponse = { url: `temp_${metadata.topicId}` }
  metadata?.callback(tempResponse)

  // 创建上传任务并添加到队列
  const taskId = metadata.topicId
  addTask(taskId, rawBlob, metadata)

  // 启动上传任务处理器
  processUploadQueue(
    // 处理Blob的函数
    async blob => {
      if (props.action === 'upload') {
        return await processForUpload(blob)
      }
      return blob
    },
    // 上传函数
    async (blob, meta, progressCallback) => {
      return await uploadTask(blob, meta, progressCallback, props.headers, props.uploadUrl)
    },
    // 发送事件的函数
    eventData => {
      emit('success', eventData)
    }
  )
}

// 错误处理
const handleError = error => {
  emit('error', error)
}

// 暴露方法
defineExpose({ start, stop, areAllUploadsComplete, getOverallUploadProgress, getUploadStatusSummary })
</script>

<style scoped>
.video-recorder {
  position: absolute;
  right: 90px;
  top: 90px;
  width: 450px;
  height: 450px;
  border: #14347f 20px solid;
  border-radius: 50%;
  overflow: hidden;
  z-index: 10;

  /* 内部视频容器居中 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  /* 保持视频容器在圆形内居中 */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .video-element {
    position: absolute;
    top: 0;
    left: 50%;
    /* 默认使用 16:9 的比例（横屏模式） */
    aspect-ratio: 1260 / 690;
    width: auto;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  /* 竖屏模式下的视频样式 */
  .video-element[data-orientation="portrait"] {
    /* 竖屏模式下使用 9:16 的比例 */
    aspect-ratio: 690 / 1260;
    width: 100%;
    height: auto;
    max-height: none;
    object-fit: contain;
  }

  .face-canvas {
    position: absolute;
    top: 0;
    left: 50%;
    /* 默认使用 16:9 的比例（横屏模式） */
    aspect-ratio: 1260 / 690;
    width: auto;
    height: 100%;
    z-index: 2;
    pointer-events: none;
  }

  /* 竖屏模式下的画布样式 */
  .face-canvas[data-orientation="portrait"] {
    /* 竖屏模式下使用 9:16 的比例 */
    aspect-ratio: 690 / 1260;
    width: 100%;
    height: auto;
    max-height: none;
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;

  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 4px;
  z-index: 1;
  font-size: 32px;

  > span {
    display: flex;
    align-items: center;
    gap: 0.5em;
  }
}

.too-fast-tip {
  font-size: 40px;
}

.zoom-slider {
  margin: 12px 0 0 0;
  width: 60%;
  z-index: 10;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  span {
    font-size: 32px;
    color: #1af117;
  }
}
</style>
