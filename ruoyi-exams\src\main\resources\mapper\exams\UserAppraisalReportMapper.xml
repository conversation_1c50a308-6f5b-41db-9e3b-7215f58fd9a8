<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.UserAppraisalReportMapper">

    <resultMap type="UserAppraisalReport" id="UserAppraisalReportResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="examId"    column="exam_id"    />
        <result property="urlName"    column="url_name"    />
        <result property="emotion"    column="emotion"    />
        <result property="emotionScore"    column="emotion_score"    />
        <result property="leastValue"    column="least_value"    />
        <result property="maximumValue"    column="maximum_value"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserAppraisalReportVo">
        select id, user_id, exam_id, url_name, emotion, emotion_score, least_value, maximum_value, create_by, create_time, update_by, update_time from user_appraisal_report
    </sql>

    <select id="selectUserAppraisalReportList" parameterType="UserAppraisalReport" resultMap="UserAppraisalReportResult">
        <include refid="selectUserAppraisalReportVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="examId != null "> and exam_id = #{examId}</if>
            <if test="emotion != null  and emotion != ''"> and emotion = #{emotion}</if>
            <if test="emotionScore != null "> and emotion_score = #{emotionScore}</if>
            <if test="leastValue != null "> and least_value = #{leastValue}</if>
            <if test="maximumValue != null "> and maximum_value = #{maximumValue}</if>
        </where>
    </select>

    <select id="selectUserAppraisalReportById" parameterType="Long" resultMap="UserAppraisalReportResult">
        <include refid="selectUserAppraisalReportVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserAppraisalReport" parameterType="UserAppraisalReport" useGeneratedKeys="true" keyProperty="id">
        insert into user_appraisal_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="examId != null">exam_id,</if>
            <if test="urlName != null">url_name,</if>
            <if test="emotion != null">emotion,</if>
            <if test="emotionScore != null">emotion_score,</if>
            <if test="leastValue != null">least_value,</if>
            <if test="maximumValue != null">maximum_value,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="examId != null">#{examId},</if>
            <if test="urlName != null">#{urlName},</if>
            <if test="emotion != null">#{emotion},</if>
            <if test="emotionScore != null">#{emotionScore},</if>
            <if test="leastValue != null">#{leastValue},</if>
            <if test="maximumValue != null">#{maximumValue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUserAppraisalReport" parameterType="UserAppraisalReport">
        update user_appraisal_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="examId != null">exam_id = #{examId},</if>
            <if test="urlName != null">url_name = #{urlName},</if>
            <if test="emotion != null">emotion = #{emotion},</if>
            <if test="emotionScore != null">emotion_score = #{emotionScore},</if>
            <if test="leastValue != null">least_value = #{leastValue},</if>
            <if test="maximumValue != null">maximum_value = #{maximumValue},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserAppraisalReportById" parameterType="Long">
        delete from user_appraisal_report where id = #{id}
    </delete>

    <delete id="deleteUserAppraisalReportByIds" parameterType="String">
        delete from user_appraisal_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
