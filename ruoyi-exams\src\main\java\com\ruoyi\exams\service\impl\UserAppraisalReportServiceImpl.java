package com.ruoyi.exams.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.exams.domain.UserAppraisalReport;
import com.ruoyi.exams.domain.UserExams;
import com.ruoyi.exams.mapper.UserAnswersMapper;
import com.ruoyi.exams.mapper.UserAppraisalReportMapper;
import com.ruoyi.exams.mapper.UserExamsMapper;
import com.ruoyi.exams.service.IUserAppraisalReportService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户测评报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class UserAppraisalReportServiceImpl implements IUserAppraisalReportService {

    private static final Logger log = LoggerFactory.getLogger(UserAppraisalReportServiceImpl.class);

    @Autowired
    private UserAppraisalReportMapper userAppraisalReportMapper;

    @Autowired
    private UserExamsMapper userExamsMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private UserAnswersMapper userAnswersMapper;

    /**
     * 查询用户测评报告
     *
     * @param id 用户测评报告主键
     * @return 用户测评报告
     */
    @Override
    public UserAppraisalReport selectUserAppraisalReportById(Long id)
    {
        return userAppraisalReportMapper.selectUserAppraisalReportById(id);
    }

    /**
     * 查询用户测评报告列表
     *
     * @param userAppraisalReport 用户测评报告
     * @return 用户测评报告
     */
    @Override
    public List<UserAppraisalReport> selectUserAppraisalReportList(UserAppraisalReport userAppraisalReport)
    {
        return userAppraisalReportMapper.selectUserAppraisalReportList(userAppraisalReport);
    }

    /**
     * 新增用户测评报告
     *
     * @param userAppraisalReport 用户测评报告
     * @return 结果
     */
    @Override
    public int insertUserAppraisalReport(UserAppraisalReport userAppraisalReport)
    {
        userAppraisalReport.setCreateBy(SecurityUtils.getUserId().toString());
        userAppraisalReport.setCreateTime(DateUtils.getNowDate());
        return userAppraisalReportMapper.insertUserAppraisalReport(userAppraisalReport);
    }

    @Override
    public int batchAdd(List<UserAppraisalReport> list) {
//        log.info("数据: {}", list);
        int i = 0;
        for (UserAppraisalReport userAppraisalReport : list) {
            Long examId = userAppraisalReport.getExamId();
            UserExams userExams = userExamsMapper.selectUserExamsById(examId);
            Long ud = userExams.getUserId();
            String createBy = userExams.getCreateBy();
            userAppraisalReport.setUserId(ud);
            userAppraisalReport.setExamId(examId);
            userAppraisalReport.setCreateBy(createBy);
            userAppraisalReport.setCreateTime(new Date());
            i += userAppraisalReportMapper.insertUserAppraisalReport(userAppraisalReport);
        }
        return i;
    }

    /**
     * 修改用户测评报告
     *
     * @param userAppraisalReport 用户测评报告
     * @return 结果
     */
    @Override
    public int updateUserAppraisalReport(UserAppraisalReport userAppraisalReport)
    {
        userAppraisalReport.setUpdateTime(DateUtils.getNowDate());
        return userAppraisalReportMapper.updateUserAppraisalReport(userAppraisalReport);
    }

    /**
     * 批量删除用户测评报告
     *
     * @param ids 需要删除的用户测评报告主键
     * @return 结果
     */
    @Override
    public int deleteUserAppraisalReportByIds(Long[] ids)
    {
        return userAppraisalReportMapper.deleteUserAppraisalReportByIds(ids);
    }

    /**
     * 删除用户测评报告信息
     *
     * @param id 用户测评报告主键
     * @return 结果
     */
    @Override
    public int deleteUserAppraisalReportById(Long id)
    {
        return userAppraisalReportMapper.deleteUserAppraisalReportById(id);
    }

    @Override
    public Map<String, Object> getReportInfo(UserAppraisalReport userAppraisalReport) {
        HashMap<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SysUser sysUser = userMapper.selectUserById(userAppraisalReport.getUserId());
        String nickName = sysUser.getNickName();
        String sex = sysUser.getSex();
        String userName = sysUser.getUserName();
        UserExams userExams = userExamsMapper.selectUserExamsById(userAppraisalReport.getExamId());
        List<String> list = userAnswersMapper.selectUrlList(userAppraisalReport.getExamId());
        for (int i = 0; i < list.size(); i++) {
            String s = list.get(i);
            if (s != null) {
                list.set(i, s.replace("original", "result"));
            }
        }
        Date startTime = userExams.getStartTime();
        Date endTime = userExams.getEndTime();
        BigDecimal score = userExams.getScore();
        Map<String, Object> user = new HashMap<>();
        user.put("nickName", nickName);
        user.put("sex", sex);
        user.put("userName", userName);
        user.put("startTime", sdf.format(startTime));
        user.put("endTime", sdf.format(endTime));
        result.put("user", user);
        Map<String, Object> report = new HashMap<>();
        report.put("total", score);
        report.put("urlList", list);
        result.put("report", report);
        return result;
    }

}
