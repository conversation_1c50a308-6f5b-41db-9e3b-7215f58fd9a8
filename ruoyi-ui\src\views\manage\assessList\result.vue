<template>
    <div class="app-container">
        <div class="text-center text-[30px] font-bold">心理健康测试</div>
        <div class="text-[20px] font-bold my-[20px]">测评人信息</div>
        <el-table :data="userInfoData" border style="width: 100%">
            <el-table-column prop="nickName" label="姓名" width="180" align="center" />
            <el-table-column prop="sex" label="性别" width="180" align="center">
                <template #default="scope">
                    <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
                </template>
            </el-table-column>
            <el-table-column prop="userName" label="登录名" align="center" />
            <el-table-column prop="startTime" label="测评时间" align="center" />
            <el-table-column label="测评用时" align="center">
                <template #default="scope">
                    <span>{{ proxy.calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="score" label="总分" align="center" />
        </el-table>
        <div class="text-[20px] font-bold my-[20px]">微表情识别</div>
        <div class="flex justify-center">
            <video ref="preview" class="videoBox" @error="nextVideo" @ended="nextVideo" :src="videoList[0]"
                crossorigin></video>
        </div>
        <div class="text-[20px] font-bold my-[20px]">微表情结果</div>
        <div class="flex items-center justify-between bg-[#F0FAFB] p-[20px]">
            <div class="w-[49%] p-[20px] bg-[#ffffff] rounded-[20px]">
                <div class="text-[20px]">心理情绪占比</div>
                <pie-chart ref="pieChartRef" />
            </div>
            <div class="w-[49%] p-[20px] bg-[#ffffff] rounded-[20px]">
                <div class="text-[20px] mb-[30px]">微表情情绪分值(%)</div>
                <micro-expression-table ref="microRef" />
            </div>
        </div>
    </div>
</template>

<script setup>
import PieChart from './components/3DPieChart' // 3D饼图
import MicroExpressionTable from './components/MicroExpressionTable' // 微表情表
import { getPercentageList, getReportInfo } from '@/api/test/examsRecord'

const { proxy } = getCurrentInstance()
const { sys_user_sex } = proxy.useDict("sys_user_sex")
const router = useRouter()
const route = useRoute()
const userInfoData = ref([])
const videoList = ref([])
const currentIndex = ref(0)
const preview = ref(null)
const microRef = ref(null)
const pieChartRef = ref(null)

/** 查询测评信息 */
function getList() {
    getReportInfo({ userId: route.query.userId, examId: route.query.id, type: 1 }).then(response => {
        let userInfo = response.data.user
        userInfo.score = response.data.report.score
        userInfoData.value = [userInfo]
        videoList.value = []
        let videoUrlList = response.data.report.urlList
        videoUrlList.forEach((element, index) => {
            if (element) {
                videoList.value.push(element)
            }
        })
    })
    getPercentageList({ examId: route.query.id }).then(res => {
        microRef.value.tableData = res.data.list
        pieChartRef.value.data[0].value = Number(res.data.pie.positive)
        pieChartRef.value.data[1].value = Number(res.data.pie.negative)
        pieChartRef.value.data[2].value = Number(res.data.pie.others)
        pieChartRef.value.initChart()
    })
}

onMounted(() => {
    getList()
    preview.value.play()
})

//视频连续播放
const nextVideo = async () => {
    if (currentIndex.value < videoList.value.length - 1) {
        currentIndex.value++ // 切换到下一个视频
        preview.value.pause()
        preview.value.src = videoList.value[currentIndex.value]
        await preview.value.load()
        await preview.value.play()
    } else {
        currentIndex.value = 0 // 如果已经是最后一个视频，可以循环到第一个或停止播放
        preview.value.pause()
        preview.value.src = videoList.value[currentIndex.value]
        await preview.value.load()
        await preview.value.play()
    }
}
</script>
<style lang="scss" scoped>
.videoBox {
    width: 50%;
    height: 500px;
    object-fit: cover;
}
</style>