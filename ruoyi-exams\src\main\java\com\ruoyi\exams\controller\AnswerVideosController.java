package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.AnswerVideos;
import com.ruoyi.exams.service.IAnswerVideosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 答题视频记录Controller
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@RestController
@RequestMapping("/videos")
public class AnswerVideosController extends BaseController {

    @Autowired
    private IAnswerVideosService answerVideosService;

    /**
     * 查询答题视频记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:videos:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnswerVideos answerVideos)
    {
        startPage();
        List<AnswerVideos> list = answerVideosService.selectAnswerVideosList(answerVideos);
        return getDataTable(list);
    }

    /**
     * 导出答题视频记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:videos:export')")
    @Log(title = "答题视频记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnswerVideos answerVideos)
    {
        List<AnswerVideos> list = answerVideosService.selectAnswerVideosList(answerVideos);
        ExcelUtil<AnswerVideos> util = new ExcelUtil<AnswerVideos>(AnswerVideos.class);
        util.exportExcel(response, list, "答题视频记录数据");
    }

    /**
     * 获取答题视频记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:videos:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(answerVideosService.selectAnswerVideosById(id));
    }

    /**
     * 新增答题视频记录
     */
    @PreAuthorize("@ss.hasPermi('system:videos:add')")
    @Log(title = "答题视频记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnswerVideos answerVideos)
    {
        return toAjax(answerVideosService.insertAnswerVideos(answerVideos));
    }

    /**
     * 修改答题视频记录
     */
    @PreAuthorize("@ss.hasPermi('system:videos:edit')")
    @Log(title = "答题视频记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnswerVideos answerVideos)
    {
        return toAjax(answerVideosService.updateAnswerVideos(answerVideos));
    }

    /**
     * 删除答题视频记录
     */
    @PreAuthorize("@ss.hasPermi('system:videos:remove')")
    @Log(title = "答题视频记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(answerVideosService.deleteAnswerVideosByIds(ids));
    }

}
