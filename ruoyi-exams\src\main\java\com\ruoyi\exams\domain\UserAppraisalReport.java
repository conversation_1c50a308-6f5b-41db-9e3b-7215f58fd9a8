package com.ruoyi.exams.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 用户测评报告对象 user_appraisal_report
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UserAppraisalReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 试卷id */
    @Excel(name = "试卷id")
    private Long examId;

    /** 视频地址名称 */
    @Excel(name = "视频地址名称")
    private String urlName;

    /** 情绪 */
    @Excel(name = "情绪")
    private String emotion;

    /** 情绪分值 */
    @Excel(name = "情绪分值")
    private Long emotionScore;

    /** 最小值 */
    @Excel(name = "最小值")
    private Long leastValue;

    /** 最大值 */
    @Excel(name = "最大值")
    private Long maximumValue;

}
