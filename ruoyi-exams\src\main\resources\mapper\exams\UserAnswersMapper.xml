<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.UserAnswersMapper">

    <resultMap type="UserAnswers" id="UserAnswersResult">
        <result property="id"    column="id"    />
        <result property="userExamId"    column="user_exam_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="optionId"    column="option_id"    />
        <result property="answerText"    column="answer_text"    />
        <result property="urlName"    column="url_name"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="videoDuration"    column="video_duration"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserAnswersVo">
        select id, user_exam_id, question_id, option_id, answer_text, url_name, video_url, video_duration, start_time, end_time, create_by, create_time, update_by, update_time from user_answers
    </sql>

    <select id="selectUserAnswersList" parameterType="UserAnswers" resultMap="UserAnswersResult">
        <include refid="selectUserAnswersVo"/>
        <where>
            <if test="userExamId != null "> and user_exam_id = #{userExamId}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="optionId != null "> and option_id = #{optionId}</if>
            <if test="answerText != null  and answerText != ''"> and answer_text = #{answerText}</if>
        </where>
    </select>

    <select id="selectUserAnswersById" parameterType="Long" resultMap="UserAnswersResult">
        <include refid="selectUserAnswersVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserAnswers" parameterType="UserAnswers" useGeneratedKeys="true" keyProperty="id">
        insert into user_answers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userExamId != null">user_exam_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="optionId != null">option_id,</if>
            <if test="answerText != null">answer_text,</if>
            <if test="urlName != null">url_name,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="videoDuration != null">video_duration,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userExamId != null">#{userExamId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="optionId != null">#{optionId},</if>
            <if test="answerText != null">#{answerText},</if>
            <if test="urlName != null">#{urlName},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="videoDuration != null">#{videoDuration},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUserAnswers" parameterType="UserAnswers">
        update user_answers
        <trim prefix="SET" suffixOverrides=",">
            <if test="userExamId != null">user_exam_id = #{userExamId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionId != null">option_id = #{optionId},</if>
            <if test="answerText != null">answer_text = #{answerText},</if>
            <if test="urlName != null">url_name = #{urlName},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="videoDuration != null">video_duration = #{videoDuration},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserAnswersById" parameterType="Long">
        delete from user_answers where id = #{id}
    </delete>

    <delete id="deleteUserAnswersByIds" parameterType="String">
        delete from user_answers where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUrlList" resultType="string">
        select video_url from user_answers where user_exam_id = #{userExamId}
    </select>

</mapper>
