package com.ruoyi.exams.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.exams.domain.Questions;
import com.ruoyi.exams.service.IQuestionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 题目Controller
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@RestController
@RequestMapping("/questions")
public class QuestionsController extends BaseController {

    @Autowired
    private IQuestionsService questionsService;

    /**
     * 查询题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:questions:list')")
    @GetMapping("/list")
    public TableDataInfo list(Questions questions)
    {
        startPage();
        List<Questions> list = questionsService.selectQuestionsList(questions);
        return getDataTable(list);
    }

    /**
     * 导出题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:questions:export')")
    @Log(title = "题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Questions questions)
    {
        List<Questions> list = questionsService.selectQuestionsList(questions);
        ExcelUtil<Questions> util = new ExcelUtil<Questions>(Questions.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 获取题目详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:questions:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(questionsService.selectQuestionsById(id));
    }

    /**
     * 新增题目
     */
    @PreAuthorize("@ss.hasPermi('system:questions:add')")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Questions questions)
    {
        return toAjax(questionsService.insertQuestions(questions));
    }

    /**
     * 修改题目
     */
    @PreAuthorize("@ss.hasPermi('system:questions:edit')")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Questions questions)
    {
        return toAjax(questionsService.updateQuestions(questions));
    }

    /**
     * 删除题目
     */
    @PreAuthorize("@ss.hasPermi('system:questions:remove')")
    @Log(title = "题目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(questionsService.deleteQuestionsByIds(ids));
    }

}
