package com.ruoyi.exams.service;

import com.ruoyi.exams.domain.Questions;

import java.util.List;

/**
 * 题目Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IQuestionsService {

    /**
     * 查询题目
     *
     * @param id 题目主键
     * @return 题目
     */
    public Questions selectQuestionsById(Long id);

    /**
     * 查询题目列表
     *
     * @param questions 题目
     * @return 题目集合
     */
    public List<Questions> selectQuestionsList(Questions questions);

    /**
     * 新增题目
     *
     * @param questions 题目
     * @return 结果
     */
    public int insertQuestions(Questions questions);

    /**
     * 修改题目
     *
     * @param questions 题目
     * @return 结果
     */
    public int updateQuestions(Questions questions);

    /**
     * 批量删除题目
     *
     * @param ids 需要删除的题目主键集合
     * @return 结果
     */
    public int deleteQuestionsByIds(Long[] ids);

    /**
     * 删除题目信息
     *
     * @param id 题目主键
     * @return 结果
     */
    public int deleteQuestionsById(Long id);

}
