<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.exams.mapper.QuestionsMapper">

    <resultMap type="Questions" id="QuestionsResult">
        <result property="id"    column="id"    />
        <result property="examId"    column="exam_id"    />
        <result property="questionText"    column="question_text"    />
        <result property="questionType"    column="question_type"    />
        <result property="score"    column="score"    />
        <result property="scoreOrder"    column="score_order"    />
        <result property="reverseScore"    column="reverse_score"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQuestionsVo">
        select id, exam_id, question_text, question_type, score, score_order, reverse_score, create_by, create_time, update_by, update_time from questions
    </sql>

    <select id="selectQuestionsList" parameterType="Questions" resultMap="QuestionsResult">
        <include refid="selectQuestionsVo"/>
        <where>
            <if test="examId != null "> and exam_id = #{examId}</if>
            <if test="questionText != null  and questionText != ''"> and question_text = #{questionText}</if>
            <if test="questionType != null "> and question_type = #{questionType}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="scoreOrder != null "> and score_order = #{scoreOrder}</if>
        </where>
    </select>

    <select id="selectQuestionsById" parameterType="Long" resultMap="QuestionsResult">
        <include refid="selectQuestionsVo"/>
        where id = #{id}
    </select>

    <insert id="insertQuestions" parameterType="Questions" useGeneratedKeys="true" keyProperty="id">
        insert into questions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="examId != null">exam_id,</if>
            <if test="questionText != null">question_text,</if>
            <if test="questionType != null">question_type,</if>
            <if test="score != null">score,</if>
            <if test="scoreOrder != null">score_order,</if>
            <if test="reverseScore != null">reverse_score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="examId != null">#{examId},</if>
            <if test="questionText != null">#{questionText},</if>
            <if test="questionType != null">#{questionType},</if>
            <if test="score != null">#{score},</if>
            <if test="scoreOrder != null">#{scoreOrder},</if>
            <if test="reverseScore != null">#{reverseScore},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateQuestions" parameterType="Questions">
        update questions
        <trim prefix="SET" suffixOverrides=",">
            <if test="examId != null">exam_id = #{examId},</if>
            <if test="questionText != null">question_text = #{questionText},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="score != null">score = #{score},</if>
            <if test="scoreOrder != null">score_order = #{scoreOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuestionsById" parameterType="Long">
        delete from questions where id = #{id}
    </delete>

    <delete id="deleteQuestionsByIds" parameterType="String">
        delete from questions where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
