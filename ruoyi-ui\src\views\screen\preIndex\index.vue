<template>
  <div class="screen-container">
    <div class="introduction">和我聊聊心情、做个心理小测试，或者看看历史记录吧～</div>
    <div class="button-container grid grid-cols-2 px-2">
      <div class="screen-button w-[100%]" @click="handleChat">聊聊现在</div>
      <div class="screen-button w-[100%]" @click="handleTest">健康心理测试</div>
      <div class="screen-button w-[100%]" @click="handleEmotion">情绪诱发测试</div>
      <div class="screen-button w-[100%]" @click="handleHistory">查记录</div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
const router = useRouter()

// #region 视频播放
const emit = defineEmits(['handlePlay'])
onMounted(() => {
  emit('handlePlay', 'pre-index')
})
// #endregion

const handleTest = () => {
  setTimeout(() => {
    router.push({ name: 'ScreenTestIndex' })
  }, 30)
}

const handleChat = () => {
  setTimeout(() => {
    router.push({ name: 'ScreenChat' })
  }, 30)
}

const handleHistory = () => {
  setTimeout(() => {
    router.push({ name: 'ScreenHistory' })
  }, 30)
}

const handleEmotion = () => {
  setTimeout(() => {
    router.push({ name: 'ScreenEmotion' })
  }, 30)
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  .introduction {
    padding: 0 218px;
    font-family: DingTalk JinBuTi;
    font-size: 80px;
    line-height: 110px;
    text-align: center;
    color: #033682;
  }
  // 按钮
  .button-container {
    margin-top: 122px;
    gap: 60px;
    .screen-button {
      // width: 600px;
      height: 260px;
      border-radius: 50px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
      box-sizing: border-box;
      border: 4px solid #ffffff;
      backdrop-filter: blur(153px);

      font-family: DingTalk JinBuTi;
      font-size: 80px;
      color: #1969ff;
      line-height: 260px;
      text-align: center;
    }
  }
}
</style>
