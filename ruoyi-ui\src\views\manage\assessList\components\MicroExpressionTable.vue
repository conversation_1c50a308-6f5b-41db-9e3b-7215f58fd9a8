<template>
  <!-- 微表情表 -->
  <el-table :data="tableData" style="width: 100%;height: 470px;">
    <el-table-column prop="emotion" label="情绪" align="center">
      <template #default="scope">
        {{ scope.row.emotion == 'Face blur, please adjust' ? '中性' : scope.row.emotion }}
      </template>
    </el-table-column>
    <el-table-column prop="percentage" label="情绪分值" align="center" />
    <el-table-column prop="minEmotionScore" label="最小值" align="center" />
    <el-table-column prop="maxEmotionScore" label="最大值" align="center" />
  </el-table>
</template>

<script setup name="MicroExpressionTable">
const tableData = ref([])
tableData.value = [
  { emotion: '开心', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' },
  { emotion: '惊讶', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' },
  { emotion: '中性', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' },
  { emotion: '压抑', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' },
  { emotion: '悲伤', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' },
  { emotion: '厌恶', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' },
  { emotion: '恐惧', percentage: '48%', maxEmotionScore: '2%', minEmotionScore: '97%' }
]
defineExpose({ tableData })
</script>

<style lang="scss" scoped>
.el-table {
  background-color: transparent;

  :deep(.el-table__inner-wrapper) {
    .el-table__header-wrapper {
      .el-table__header {
        width: unset !important;

        .el-table__cell {
          background-color: #ffffff !important;
          height: 50px !important;

          &:first-child {
            border-top-left-radius: 32px;
            border-bottom-left-radius: 32px;
          }

          &:last-child {
            border-top-right-radius: 32px;
            border-bottom-right-radius: 32px;
          }

          .cell {
            font-family: DingTalk JinBuTi;
            font-size: 26px;
            font-weight: normal;
            line-height: 26px;
            color: #033682;
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-scrollbar__view {
        .el-table__body {
          width: unset !important;

          .el-table__row {
            background-color: transparent;

            .el-table__cell {
              padding: 0;

              .cell {
                font-size: 22px;
                line-height: 55px;
                color: #3d3d3d;
              }

              &:first-child {
                .cell {
                  font-family: AlibabaPuHuiTiBold;
                }
              }

              &:not(:first-child) {
                .cell {
                  font-family: AlibabaPuHuiTiRegular;
                }
              }
            }
          }
        }
      }
    }
  }

  font-size: 36px;
}
</style>
